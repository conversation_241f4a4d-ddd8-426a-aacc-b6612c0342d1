<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_Cxvgam4Service extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.cxvgam4' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.cxvgam4'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'competenceRepository' => ['privates', 'App\\Repository\\CompetenceRepository', 'getCompetenceRepositoryService', true],
            'quizRepository' => ['privates', 'App\\Repository\\QuizRepository', 'getQuizRepositoryService', true],
            'sousCompetenceRepository' => ['privates', 'App\\Repository\\SousCompetenceRepository', 'getSousCompetenceRepositoryService', true],
        ], [
            'competenceRepository' => 'App\\Repository\\CompetenceRepository',
            'quizRepository' => 'App\\Repository\\QuizRepository',
            'sousCompetenceRepository' => 'App\\Repository\\SousCompetenceRepository',
        ]);
    }
}

<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getMonolog_Handler_ChatbotService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'monolog.handler.chatbot' shared service.
     *
     * @return \Monolog\Handler\RotatingFileHandler
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'monolog'.\DIRECTORY_SEPARATOR.'monolog'.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Monolog'.\DIRECTORY_SEPARATOR.'Handler'.\DIRECTORY_SEPARATOR.'RotatingFileHandler.php';

        $container->privates['monolog.handler.chatbot'] = $instance = new \Monolog\Handler\RotatingFileHandler((\dirname(__DIR__, 3).''.\DIRECTORY_SEPARATOR.'log/chatbot.log'), 10, 'info', true, NULL, false);

        $instance->pushProcessor(($container->privates['monolog.processor.psr_log_message'] ??= new \Monolog\Processor\PsrLogMessageProcessor()));
        $instance->setFilenameFormat('{filename}-{date}', 'Y-m-d');

        return $instance;
    }
}

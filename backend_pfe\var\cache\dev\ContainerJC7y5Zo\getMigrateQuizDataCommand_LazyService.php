<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getMigrateQuizDataCommand_LazyService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.App\Command\MigrateQuizDataCommand.lazy' shared service.
     *
     * @return \Symfony\Component\Console\Command\LazyCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'LazyCommand.php';

        return $container->privates['.App\\Command\\MigrateQuizDataCommand.lazy'] = new \Symfony\Component\Console\Command\LazyCommand('app:migrate-quiz-data', [], 'Migre les données des quiz vers les nouvelles entités Competence, SousCompetence et Action', false, #[\Closure(name: 'App\\Command\\MigrateQuizDataCommand')] fn (): \App\Command\MigrateQuizDataCommand => ($container->privates['App\\Command\\MigrateQuizDataCommand'] ?? $container->load('getMigrateQuizDataCommandService')));
    }
}

<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getEvenementControllerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the public 'App\Controller\EvenementController' shared autowired service.
     *
     * @return \App\Controller\EvenementController
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'framework-bundle'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AbstractController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'EvenementController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'WebSocketNotificationService.php';

        $container->services['App\\Controller\\EvenementController'] = $instance = new \App\Controller\EvenementController(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), ($container->privates['App\\Repository\\EvenementRepository'] ?? $container->load('getEvenementRepositoryService')), ($container->privates['App\\Repository\\AdministrateurRepository'] ?? $container->load('getAdministrateurRepositoryService')), ($container->privates['App\\Repository\\ApprenantRepository'] ?? $container->load('getApprenantRepositoryService')), ($container->privates['App\\Repository\\FormateurRepository'] ?? $container->load('getFormateurRepositoryService')), ($container->privates['security.helper'] ?? $container->load('getSecurity_HelperService')), ($container->privates['debug.serializer'] ?? self::getDebug_SerializerService($container)), new \App\Service\WebSocketNotificationService(($container->privates['monolog.logger'] ?? self::getMonolog_LoggerService($container)), $container));

        $instance->setContainer(($container->privates['.service_locator.O2p6Lk7'] ?? $container->load('get_ServiceLocator_O2p6Lk7Service'))->withContext('App\\Controller\\EvenementController', $container));

        return $instance;
    }
}

<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getAssignCoursesToApprenantCommandService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Command\AssignCoursesToApprenantCommand' shared autowired service.
     *
     * @return \App\Command\AssignCoursesToApprenantCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'AssignCoursesToApprenantCommand.php';

        $container->privates['App\\Command\\AssignCoursesToApprenantCommand'] = $instance = new \App\Command\AssignCoursesToApprenantCommand(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), ($container->privates['App\\Repository\\ApprenantRepository'] ?? $container->load('getApprenantRepositoryService')), ($container->privates['App\\Repository\\CoursRepository'] ?? $container->load('getCoursRepositoryService')));

        $instance->setName('app:assign-courses');
        $instance->setDescription('Assign courses to apprenants');

        return $instance;
    }
}

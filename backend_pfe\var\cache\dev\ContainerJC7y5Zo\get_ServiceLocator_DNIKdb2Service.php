<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_DNIKdb2Service extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.DNIKdb2' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.DNIKdb2'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'App\\Controller\\ActionController::create' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\ActionController::delete' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\ActionController::list' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\ActionController::show' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\ActionController::update' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\AdminController::addUser' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AdminController::editUser' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AuthController::forgotPassword' => ['privates', '.service_locator.zcZ.KbZ', 'get_ServiceLocator_ZcZ_KbZService', true],
            'App\\Controller\\CompetenceController::create' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\CompetenceController::delete' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\CompetenceController::list' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\CompetenceController::show' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\CompetenceController::update' => ['privates', '.service_locator.j6IoHy0', 'get_ServiceLocator_J6IoHy0Service', true],
            'App\\Controller\\FormateurController::getApprenantCoursAllCategories' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\FormateurController::getApprenantCoursByCategory' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\FormateurController::getCours' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController::createAction' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\QuizController::createCompetence' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\QuizController::createQuizAction' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\QuizController::createSousCompetence' => ['privates', '.service_locator.f0CMdBy', 'get_ServiceLocator_F0CMdByService', true],
            'App\\Controller\\QuizController::delete' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController::deleteAction' => ['privates', '.service_locator.MFVMisY', 'get_ServiceLocator_MFVMisYService', true],
            'App\\Controller\\QuizController::deleteByIdModule' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController::deleteCompetence' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\QuizController::deleteQuizAction' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\QuizController::deleteQuizActionById' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\QuizController::deleteSousCompetence' => ['privates', '.service_locator.cxvgam4', 'get_ServiceLocator_Cxvgam4Service', true],
            'App\\Controller\\QuizController::list' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController::show' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController::update' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController::updateAction' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\QuizController::updateCompetence' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\QuizController::updateQuizAction' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\QuizController::updateQuizActionById' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\QuizController::updateSousCompetence' => ['privates', '.service_locator.cxvgam4', 'get_ServiceLocator_Cxvgam4Service', true],
            'App\\Controller\\QuizController::updateSousCompetenceById' => ['privates', '.service_locator.cxvgam4', 'get_ServiceLocator_Cxvgam4Service', true],
            'App\\Controller\\SousCompetenceController::create' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\SousCompetenceController::delete' => ['privates', '.service_locator.PQOdg4m', 'get_ServiceLocator_PQOdg4mService', true],
            'App\\Controller\\SousCompetenceController::list' => ['privates', '.service_locator.PQOdg4m', 'get_ServiceLocator_PQOdg4mService', true],
            'App\\Controller\\SousCompetenceController::show' => ['privates', '.service_locator.PQOdg4m', 'get_ServiceLocator_PQOdg4mService', true],
            'App\\Controller\\SousCompetenceController::update' => ['privates', '.service_locator.PQOdg4m', 'get_ServiceLocator_PQOdg4mService', true],
            'App\\Kernel::loadRoutes' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'App\\Kernel::registerContainerConfiguration' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'kernel::loadRoutes' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'kernel::registerContainerConfiguration' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'App\\Controller\\ActionController:create' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\ActionController:delete' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\ActionController:list' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\ActionController:show' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\ActionController:update' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\AdminController:addUser' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AdminController:editUser' => ['privates', '.service_locator.Mhqdd2r', 'get_ServiceLocator_Mhqdd2rService', true],
            'App\\Controller\\AuthController:forgotPassword' => ['privates', '.service_locator.zcZ.KbZ', 'get_ServiceLocator_ZcZ_KbZService', true],
            'App\\Controller\\CompetenceController:create' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\CompetenceController:delete' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\CompetenceController:list' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\CompetenceController:show' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\CompetenceController:update' => ['privates', '.service_locator.j6IoHy0', 'get_ServiceLocator_J6IoHy0Service', true],
            'App\\Controller\\FormateurController:getApprenantCoursAllCategories' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\FormateurController:getApprenantCoursByCategory' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\FormateurController:getCours' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController:createAction' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\QuizController:createCompetence' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\QuizController:createQuizAction' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\QuizController:createSousCompetence' => ['privates', '.service_locator.f0CMdBy', 'get_ServiceLocator_F0CMdByService', true],
            'App\\Controller\\QuizController:delete' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController:deleteAction' => ['privates', '.service_locator.MFVMisY', 'get_ServiceLocator_MFVMisYService', true],
            'App\\Controller\\QuizController:deleteByIdModule' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController:deleteCompetence' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\QuizController:deleteQuizAction' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\QuizController:deleteQuizActionById' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\QuizController:deleteSousCompetence' => ['privates', '.service_locator.cxvgam4', 'get_ServiceLocator_Cxvgam4Service', true],
            'App\\Controller\\QuizController:list' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController:show' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController:update' => ['privates', '.service_locator.Cj4aUBQ', 'get_ServiceLocator_Cj4aUBQService', true],
            'App\\Controller\\QuizController:updateAction' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\QuizController:updateCompetence' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\QuizController:updateQuizAction' => ['privates', '.service_locator.OrpkBWh', 'get_ServiceLocator_OrpkBWhService', true],
            'App\\Controller\\QuizController:updateQuizActionById' => ['privates', '.service_locator.v5NSJjB', 'get_ServiceLocator_V5NSJjBService', true],
            'App\\Controller\\QuizController:updateSousCompetence' => ['privates', '.service_locator.cxvgam4', 'get_ServiceLocator_Cxvgam4Service', true],
            'App\\Controller\\QuizController:updateSousCompetenceById' => ['privates', '.service_locator.cxvgam4', 'get_ServiceLocator_Cxvgam4Service', true],
            'App\\Controller\\SousCompetenceController:create' => ['privates', '.service_locator.mOufrFG', 'get_ServiceLocator_MOufrFGService', true],
            'App\\Controller\\SousCompetenceController:delete' => ['privates', '.service_locator.PQOdg4m', 'get_ServiceLocator_PQOdg4mService', true],
            'App\\Controller\\SousCompetenceController:list' => ['privates', '.service_locator.PQOdg4m', 'get_ServiceLocator_PQOdg4mService', true],
            'App\\Controller\\SousCompetenceController:show' => ['privates', '.service_locator.PQOdg4m', 'get_ServiceLocator_PQOdg4mService', true],
            'App\\Controller\\SousCompetenceController:update' => ['privates', '.service_locator.PQOdg4m', 'get_ServiceLocator_PQOdg4mService', true],
            'kernel:loadRoutes' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
            'kernel:registerContainerConfiguration' => ['privates', '.service_locator.y4_Zrx.', 'get_ServiceLocator_Y4Zrx_Service', true],
        ], [
            'App\\Controller\\ActionController::create' => '?',
            'App\\Controller\\ActionController::delete' => '?',
            'App\\Controller\\ActionController::list' => '?',
            'App\\Controller\\ActionController::show' => '?',
            'App\\Controller\\ActionController::update' => '?',
            'App\\Controller\\AdminController::addUser' => '?',
            'App\\Controller\\AdminController::editUser' => '?',
            'App\\Controller\\AuthController::forgotPassword' => '?',
            'App\\Controller\\CompetenceController::create' => '?',
            'App\\Controller\\CompetenceController::delete' => '?',
            'App\\Controller\\CompetenceController::list' => '?',
            'App\\Controller\\CompetenceController::show' => '?',
            'App\\Controller\\CompetenceController::update' => '?',
            'App\\Controller\\FormateurController::getApprenantCoursAllCategories' => '?',
            'App\\Controller\\FormateurController::getApprenantCoursByCategory' => '?',
            'App\\Controller\\FormateurController::getCours' => '?',
            'App\\Controller\\QuizController::createAction' => '?',
            'App\\Controller\\QuizController::createCompetence' => '?',
            'App\\Controller\\QuizController::createQuizAction' => '?',
            'App\\Controller\\QuizController::createSousCompetence' => '?',
            'App\\Controller\\QuizController::delete' => '?',
            'App\\Controller\\QuizController::deleteAction' => '?',
            'App\\Controller\\QuizController::deleteByIdModule' => '?',
            'App\\Controller\\QuizController::deleteCompetence' => '?',
            'App\\Controller\\QuizController::deleteQuizAction' => '?',
            'App\\Controller\\QuizController::deleteQuizActionById' => '?',
            'App\\Controller\\QuizController::deleteSousCompetence' => '?',
            'App\\Controller\\QuizController::list' => '?',
            'App\\Controller\\QuizController::show' => '?',
            'App\\Controller\\QuizController::update' => '?',
            'App\\Controller\\QuizController::updateAction' => '?',
            'App\\Controller\\QuizController::updateCompetence' => '?',
            'App\\Controller\\QuizController::updateQuizAction' => '?',
            'App\\Controller\\QuizController::updateQuizActionById' => '?',
            'App\\Controller\\QuizController::updateSousCompetence' => '?',
            'App\\Controller\\QuizController::updateSousCompetenceById' => '?',
            'App\\Controller\\SousCompetenceController::create' => '?',
            'App\\Controller\\SousCompetenceController::delete' => '?',
            'App\\Controller\\SousCompetenceController::list' => '?',
            'App\\Controller\\SousCompetenceController::show' => '?',
            'App\\Controller\\SousCompetenceController::update' => '?',
            'App\\Kernel::loadRoutes' => '?',
            'App\\Kernel::registerContainerConfiguration' => '?',
            'kernel::loadRoutes' => '?',
            'kernel::registerContainerConfiguration' => '?',
            'App\\Controller\\ActionController:create' => '?',
            'App\\Controller\\ActionController:delete' => '?',
            'App\\Controller\\ActionController:list' => '?',
            'App\\Controller\\ActionController:show' => '?',
            'App\\Controller\\ActionController:update' => '?',
            'App\\Controller\\AdminController:addUser' => '?',
            'App\\Controller\\AdminController:editUser' => '?',
            'App\\Controller\\AuthController:forgotPassword' => '?',
            'App\\Controller\\CompetenceController:create' => '?',
            'App\\Controller\\CompetenceController:delete' => '?',
            'App\\Controller\\CompetenceController:list' => '?',
            'App\\Controller\\CompetenceController:show' => '?',
            'App\\Controller\\CompetenceController:update' => '?',
            'App\\Controller\\FormateurController:getApprenantCoursAllCategories' => '?',
            'App\\Controller\\FormateurController:getApprenantCoursByCategory' => '?',
            'App\\Controller\\FormateurController:getCours' => '?',
            'App\\Controller\\QuizController:createAction' => '?',
            'App\\Controller\\QuizController:createCompetence' => '?',
            'App\\Controller\\QuizController:createQuizAction' => '?',
            'App\\Controller\\QuizController:createSousCompetence' => '?',
            'App\\Controller\\QuizController:delete' => '?',
            'App\\Controller\\QuizController:deleteAction' => '?',
            'App\\Controller\\QuizController:deleteByIdModule' => '?',
            'App\\Controller\\QuizController:deleteCompetence' => '?',
            'App\\Controller\\QuizController:deleteQuizAction' => '?',
            'App\\Controller\\QuizController:deleteQuizActionById' => '?',
            'App\\Controller\\QuizController:deleteSousCompetence' => '?',
            'App\\Controller\\QuizController:list' => '?',
            'App\\Controller\\QuizController:show' => '?',
            'App\\Controller\\QuizController:update' => '?',
            'App\\Controller\\QuizController:updateAction' => '?',
            'App\\Controller\\QuizController:updateCompetence' => '?',
            'App\\Controller\\QuizController:updateQuizAction' => '?',
            'App\\Controller\\QuizController:updateQuizActionById' => '?',
            'App\\Controller\\QuizController:updateSousCompetence' => '?',
            'App\\Controller\\QuizController:updateSousCompetenceById' => '?',
            'App\\Controller\\SousCompetenceController:create' => '?',
            'App\\Controller\\SousCompetenceController:delete' => '?',
            'App\\Controller\\SousCompetenceController:list' => '?',
            'App\\Controller\\SousCompetenceController:show' => '?',
            'App\\Controller\\SousCompetenceController:update' => '?',
            'kernel:loadRoutes' => '?',
            'kernel:registerContainerConfiguration' => '?',
        ]);
    }
}

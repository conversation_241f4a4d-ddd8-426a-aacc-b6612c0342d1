// API Configuration for mobile_pfe
// This should match the backend_pfe API configuration exactly like IMPORTANT: Pour le développement mobile, utilisez l'adresse IP de votre ordinateur
// au lieu de 127.0.0.1 qui fait référence à l'appareil mobile lui-même
// Pour les émulateurs Android, utilisez ******** qui est l'adresse spéciale pour accéder à l'hôte
// Pour les appareils physiques, utilisez l'adresse IP de votre ordinateur sur le réseau local (ex: *************)

// IMPORTANT: Pour le développement mobile, utilisez l'adresse IP de votre ordinateur
// au lieu de 127.0.0.1 qui fait référence à l'appareil mobile lui-même
// Pour les émulateurs Android, utilisez ******** qui est l'adresse spéciale pour accéder à l'hôte
// Pour les appareils physiques, utilisez l'adresse IP de votre ordinateur sur le réseau local (ex: *************)
export const API_URL = "https://**********:8000/api";

// WebSocket URL for real-time features
export const WS_URL = "wss://********:8000/ws";

// File upload configuration
export const UPLOAD_URL = "https://********:8000/uploads";

// Maximum file size for uploads (in bytes)
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// Supported file types for uploads
export const SUPPORTED_FILE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
];

// API timeout configuration
export const API_TIMEOUT = 30000; // 30 seconds

// Request retry configuration
export const MAX_RETRIES = 3;
export const RETRY_DELAY = 1000; // 1 second

// Cache configuration
export const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Pagination defaults
export const DEFAULT_PAGE_SIZE = 20;

// App configuration
export const APP_CONFIG = {
  name: "Mobile PFE",
  version: "1.0.0",
  environment: __DEV__ ? "development" : "production",
};

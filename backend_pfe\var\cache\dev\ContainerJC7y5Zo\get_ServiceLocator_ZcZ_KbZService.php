<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_ZcZ_KbZService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.zcZ.KbZ' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.zcZ.KbZ'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'emailService' => ['privates', 'App\\Service\\EmailService', 'getEmailServiceService', true],
        ], [
            'emailService' => 'App\\Service\\EmailService',
        ]);
    }
}

<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getUpdateEvaluationIdmoduleCommandService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Command\UpdateEvaluationIdmoduleCommand' shared autowired service.
     *
     * @return \App\Command\UpdateEvaluationIdmoduleCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'UpdateEvaluationIdmoduleCommand.php';

        $container->privates['App\\Command\\UpdateEvaluationIdmoduleCommand'] = $instance = new \App\Command\UpdateEvaluationIdmoduleCommand(($container->privates['App\\Repository\\EvaluationRepository'] ?? $container->load('getEvaluationRepositoryService')), ($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)));

        $instance->setName('app:update-evaluation-idmodule');
        $instance->setDescription('Update idmodule field in evaluation table from associated quiz');

        return $instance;
    }
}

<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getSynchronizeIdmoduleCommandService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Command\SynchronizeIdmoduleCommand' shared autowired service.
     *
     * @return \App\Command\SynchronizeIdmoduleCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'SynchronizeIdmoduleCommand.php';

        $container->privates['App\\Command\\SynchronizeIdmoduleCommand'] = $instance = new \App\Command\SynchronizeIdmoduleCommand(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)));

        $instance->setName('app:synchronize-idmodule');
        $instance->setDescription('Synchronise les champs idmodule dans les tables Competence et Action avec les valeurs de IDModule dans Quiz');

        return $instance;
    }
}

import React, { useState, useEffect, useContext } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Linking,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { ThemeContext } from "../../contexts/ThemeContext";
import coursService from "../../services/coursService";
import certificateService from "../../services/certificateService";
import LoadingScreen from "../../components/LoadingScreen";

const ModuleItem = ({ module, onPress, completed }) => {
  const { theme } = useContext(ThemeContext);

  return (
    <TouchableOpacity
      style={[
        styles.moduleItem,
        { borderBottomColor: theme.border },
        completed && [
          styles.completedModule,
          { backgroundColor: `${theme.success}10` },
        ],
      ]}
      onPress={() => onPress(module)}
    >
      <View
        style={[
          styles.moduleIcon,
          completed && [
            styles.completedModuleIcon,
            { backgroundColor: `${theme.success}20` },
          ],
        ]}
      >
        <MaterialCommunityIcons
          name={completed ? "check-circle" : "play-circle-outline"}
          size={24}
          color={completed ? theme.success : theme.primary}
        />
      </View>
      <View style={styles.moduleContent}>
        <Text style={[styles.moduleTitle, { color: theme.text.primary }]}>
          {module.title}
        </Text>
        {module.duration && (
          <Text
            style={[styles.moduleDuration, { color: theme.text.secondary }]}
          >
            <MaterialCommunityIcons
              name="clock-outline"
              size={14}
              color={theme.text.secondary}
            />{" "}
            {module.duration}
          </Text>
        )}
      </View>
      <MaterialCommunityIcons
        name="chevron-right"
        size={20}
        color={theme.text.secondary}
      />
    </TouchableOpacity>
  );
};

const QuizItem = ({ quiz, onPress, completed }) => {
  const { theme } = useContext(ThemeContext);

  return (
    <TouchableOpacity
      style={[
        styles.quizItem,
        { borderBottomColor: theme.border },
        completed && [
          styles.completedQuiz,
          { backgroundColor: `${theme.success}10` },
        ],
      ]}
      onPress={() => onPress(quiz)}
    >
      <View
        style={[
          styles.quizIcon,
          completed && [
            styles.completedQuizIcon,
            { backgroundColor: `${theme.success}20` },
          ],
        ]}
      >
        <MaterialCommunityIcons
          name={completed ? "check-circle" : "file-document-outline"}
          size={24}
          color={completed ? theme.success : theme.secondary}
        />
      </View>
      <View style={styles.quizContent}>
        <Text style={[styles.quizTitle, { color: theme.text.primary }]}>
          {quiz.title}
        </Text>
        {quiz.questionCount && (
          <Text style={[styles.quizInfo, { color: theme.text.secondary }]}>
            {quiz.questionCount} questions
            {quiz.duration && ` • ${quiz.duration}`}
          </Text>
        )}
      </View>
      <MaterialCommunityIcons
        name="chevron-right"
        size={20}
        color={theme.text.secondary}
      />
    </TouchableOpacity>
  );
};

const CourseDetailScreen = ({ route, navigation }) => {
  const { courseId } = route.params;
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [course, setCourse] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCourseDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        const courseData = await coursService.getCourseDetails(courseId);

        // Fetch quizzes for this course
        try {
          const quizzesData = await coursService.getCourseQuizzes(courseId);
          courseData.quizzes = quizzesData.quizzes || [];
        } catch (quizError) {
          console.error("Error fetching course quizzes:", quizError);
          courseData.quizzes = [];
        }

        setCourse(courseData);

        // Update navigation title
        navigation.setOptions({
          title: courseData.titre || courseData.title || "Détails du cours",
          headerStyle: {
            backgroundColor: theme.card,
          },
          headerTintColor: theme.text.primary,
        });

        setLoading(false);
      } catch (err) {
        console.error("Error fetching course details:", err);
        setError("Impossible de charger les détails du cours");
        setLoading(false);
      }
    };

    fetchCourseDetails();
  }, [courseId, theme]);

  const handleModulePress = (module) => {
    // Navigate to module content or mark as completed
    Alert.alert(
      "Module",
      `Voulez-vous commencer le module "${module.title}" ?`,
      [
        {
          text: "Annuler",
          style: "cancel",
        },
        {
          text: "Commencer",
          onPress: async () => {
            try {
              // Mark module as started/completed
              await coursService.markModuleCompleted(courseId, module.id);

              // Refresh course details
              const updatedCourse = await coursService.getCourseDetails(
                courseId
              );
              setCourse(updatedCourse);

              Alert.alert("Succès", "Module marqué comme complété");
            } catch (error) {
              console.error("Error marking module as completed:", error);
              Alert.alert(
                "Erreur",
                "Impossible de marquer le module comme complété"
              );
            }
          },
        },
      ]
    );
  };

  const handleQuizPress = (quiz) => {
    // Navigate to quiz
    Alert.alert("Quiz", `Voulez-vous commencer le quiz "${quiz.title}" ?`, [
      {
        text: "Annuler",
        style: "cancel",
      },
      {
        text: "Commencer",
        onPress: () => {
          // Navigate to quiz screen with proper quiz data
          navigation.navigate("Quiz", {
            quiz: {
              id: quiz.id,
              IDModule: quiz.IDModule || quiz.idmodule,
              title: quiz.title || quiz.nom_fr || quiz.Nom_FR || quiz.titre,
              questions: quiz.questions || [],
              duration: quiz.duration || 30,
              courseId: courseId,
            },
          });
        },
      },
    ]);
  };

  // Certificate generation - matching frontend_pfe functionality
  const handleGenerateCertificate = async () => {
    try {
      Alert.alert(
        "Générer le certificat",
        "Voulez-vous générer votre certificat pour ce cours ?",
        [
          { text: "Annuler", style: "cancel" },
          {
            text: "Générer",
            onPress: async () => {
              try {
                const result = await certificateService.generateCertificate(
                  courseId
                );
                if (result.success) {
                  Alert.alert(
                    "Certificat généré",
                    "Votre certificat a été généré avec succès ! Pour le télécharger, veuillez utiliser la version web de l'application.",
                    [
                      { text: "OK" },
                      {
                        text: "Ouvrir la version web",
                        onPress: () => {
                          // Open web version for certificate download
                          Linking.openURL(
                            "https://votre-site-web.com/apprenant/cours/" +
                              courseId
                          );
                        },
                      },
                    ]
                  );
                  // Refresh course data to show the certificate
                  const updatedCourse = await coursService.getCourseDetails(
                    courseId
                  );
                  setCourse(updatedCourse);
                } else {
                  Alert.alert(
                    "Erreur",
                    result.message || "Impossible de générer le certificat"
                  );
                }
              } catch (error) {
                console.error("Error generating certificate:", error);
                Alert.alert("Erreur", "Impossible de générer le certificat");
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error("Error in certificate generation:", error);
    }
  };

  if (loading) {
    return <LoadingScreen message="Chargement des détails du cours..." />;
  }

  if (error) {
    return (
      <View
        style={[styles.errorContainer, { backgroundColor: theme.background }]}
      >
        <MaterialCommunityIcons
          name="alert-circle"
          size={48}
          color={theme.danger}
        />
        <Text style={[styles.errorText, { color: theme.text.primary }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.primary }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.retryButtonText, { color: theme.text.inverse }]}>
            Retour
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!course) {
    return (
      <View
        style={[styles.errorContainer, { backgroundColor: theme.background }]}
      >
        <MaterialCommunityIcons
          name="alert-circle"
          size={48}
          color={theme.danger}
        />
        <Text style={[styles.errorText, { color: theme.text.primary }]}>
          Cours non trouvé
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.primary }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={[styles.retryButtonText, { color: theme.text.inverse }]}>
            Retour
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.background }]}
    >
      {/* Course Header */}
      <View
        style={[
          styles.courseHeader,
          { backgroundColor: theme.card, borderBottomColor: theme.border },
        ]}
      >
        <View style={styles.courseInfo}>
          {course.category && (
            <View
              style={[
                styles.categoryContainer,
                {
                  backgroundColor: isDarkMode
                    ? `${theme.primary}30`
                    : "#EEF2FF",
                },
              ]}
            >
              <Text style={[styles.category, { color: theme.primary }]}>
                {course.category}
              </Text>
            </View>
          )}

          <Text style={[styles.courseTitle, { color: theme.text.primary }]}>
            {course.titre || course.title}
          </Text>

          {course.description && (
            <Text
              style={[
                styles.courseDescription,
                { color: theme.text.secondary },
              ]}
            >
              {course.description}
            </Text>
          )}

          <View style={styles.courseStats}>
            {course.duration && (
              <View style={styles.statItem}>
                <MaterialCommunityIcons
                  name="clock-outline"
                  size={16}
                  color={theme.text.secondary}
                />
                <Text
                  style={[styles.statText, { color: theme.text.secondary }]}
                >
                  {course.duration}
                </Text>
              </View>
            )}

            {course.modules && (
              <View style={styles.statItem}>
                <MaterialCommunityIcons
                  name="book-open-variant"
                  size={16}
                  color={theme.text.secondary}
                />
                <Text
                  style={[styles.statText, { color: theme.text.secondary }]}
                >
                  {course.modules.length} modules
                </Text>
              </View>
            )}

            {course.quizzes && (
              <View style={styles.statItem}>
                <MaterialCommunityIcons
                  name="file-document-outline"
                  size={16}
                  color={theme.text.secondary}
                />
                <Text
                  style={[styles.statText, { color: theme.text.secondary }]}
                >
                  {course.quizzes.length} quiz
                </Text>
              </View>
            )}
          </View>

          {/* Progress Bar */}
          {course.progress !== undefined && (
            <View style={styles.progressContainer}>
              <View
                style={[styles.progressBar, { backgroundColor: theme.border }]}
              >
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${course.progress}%`,
                      backgroundColor: theme.primary,
                    },
                  ]}
                />
              </View>
              <Text style={[styles.progressText, { color: theme.primary }]}>
                {course.progress}% complété
              </Text>
            </View>
          )}

          {/* Certificate Button - matching frontend_pfe functionality */}
          {course.progress === 100 && !course.certificate && (
            <TouchableOpacity
              style={[
                styles.certificateButton,
                {
                  backgroundColor: isDarkMode ? "#065f4620" : "#dcfce7",
                  borderColor: isDarkMode ? "#16a34a" : "#22c55e",
                },
              ]}
              onPress={handleGenerateCertificate}
            >
              <MaterialCommunityIcons
                name="certificate"
                size={20}
                color={isDarkMode ? "#22c55e" : "#16a34a"}
              />
              <Text
                style={[
                  styles.certificateButtonText,
                  { color: isDarkMode ? "#22c55e" : "#16a34a" },
                ]}
              >
                Obtenir votre certificat
              </Text>
            </TouchableOpacity>
          )}

          {/* Certificate Info - if certificate exists */}
          {course.certificate && (
            <View
              style={[
                styles.certificateInfo,
                {
                  backgroundColor: isDarkMode ? "#1e40af20" : "#dbeafe",
                  borderColor: isDarkMode ? "#3b82f6" : "#60a5fa",
                },
              ]}
            >
              <MaterialCommunityIcons
                name="certificate"
                size={20}
                color={isDarkMode ? "#60a5fa" : "#3b82f6"}
              />
              <Text
                style={[
                  styles.certificateInfoText,
                  { color: isDarkMode ? "#60a5fa" : "#3b82f6" },
                ]}
              >
                Certificat obtenu le{" "}
                {new Date(
                  course.certificate.dateObtention ||
                    course.certificate.date_obtention
                ).toLocaleDateString("fr-FR")}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Course Content */}
      <View
        style={[styles.contentContainer, { backgroundColor: theme.background }]}
      >
        <Text style={[styles.sectionTitle, { color: theme.text.primary }]}>
          Contenu du cours
        </Text>

        {/* Modules */}
        {course.modules && course.modules.length > 0 ? (
          <View
            style={[
              styles.modulesContainer,
              { backgroundColor: theme.card, borderColor: theme.border },
            ]}
          >
            {course.modules.map((module, index) => (
              <ModuleItem
                key={index}
                module={module}
                onPress={handleModulePress}
                completed={module.completed}
              />
            ))}
          </View>
        ) : (
          <Text
            style={[
              styles.emptyText,
              {
                color: theme.text.secondary,
                backgroundColor: theme.card,
                borderColor: theme.border,
              },
            ]}
          >
            Aucun module disponible
          </Text>
        )}

        {/* Quizzes */}
        {course.quizzes && course.quizzes.length > 0 && (
          <>
            <Text
              style={[
                styles.sectionTitle,
                { marginTop: 24, color: theme.text.primary },
              ]}
            >
              Quiz
            </Text>
            <View
              style={[
                styles.quizzesContainer,
                { backgroundColor: theme.card, borderColor: theme.border },
              ]}
            >
              {course.quizzes.map((quiz, index) => (
                <QuizItem
                  key={index}
                  quiz={quiz}
                  onPress={handleQuizPress}
                  completed={quiz.completed}
                />
              ))}
            </View>
          </>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  courseHeader: {
    borderBottomWidth: 1,
  },

  courseInfo: {
    padding: 16,
  },
  categoryContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
    marginBottom: 8,
  },
  category: {
    fontSize: 12,
    fontWeight: "500",
  },
  courseTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  courseDescription: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  courseStats: {
    flexDirection: "row",
    marginBottom: 16,
    gap: 16,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  statText: {
    marginLeft: 4,
    fontSize: 14,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontWeight: "500",
  },
  certificateButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginTop: 16,
  },
  certificateButtonText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 8,
  },
  certificateInfo: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    marginTop: 16,
  },
  certificateInfoText: {
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 8,
  },
  contentContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
  },
  modulesContainer: {
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 1,
  },
  moduleItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
  },
  completedModule: {
    // Background color added in component
  },
  moduleIcon: {
    marginRight: 12,
  },
  completedModuleIcon: {
    borderRadius: 20,
  },
  moduleContent: {
    flex: 1,
  },
  moduleTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  moduleDuration: {
    fontSize: 14,
  },
  quizzesContainer: {
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 1,
  },
  quizItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
  },
  completedQuiz: {
    // Background color added in component
  },
  quizIcon: {
    marginRight: 12,
  },
  completedQuizIcon: {
    borderRadius: 20,
  },
  quizContent: {
    flex: 1,
  },
  quizTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  quizInfo: {
    fontSize: 14,
  },
  emptyText: {
    fontSize: 14,
    textAlign: "center",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 16,
    textAlign: "center",
  },
  retryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
});

export default CourseDetailScreen;

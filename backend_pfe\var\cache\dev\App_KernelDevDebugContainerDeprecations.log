a:15:{i:0;a:6:{s:4:"type";i:16384;s:7:"message";s:201:"Since doctrine/doctrine-bundle 2.12: The default value of "doctrine.orm.controller_resolver.auto_mapping" will be changed from `true` to `false`. Explicitly configure `true` to keep existing behaviour.";s:4:"file";s:196:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:504;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:196:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:122;s:8:"function";s:7:"ormLoad";s:5:"class";s:68:"Doctrine\Bundle\DoctrineBundle\DependencyInjection\DoctrineExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:1;a:6:{s:4:"type";i:16384;s:7:"message";s:173:"Since doctrine/doctrine-bundle 2.13: Enabling the controller resolver automapping feature has been deprecated. Symfony Mapped Route Parameters should be used as replacement.";s:4:"file";s:196:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:509;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:196:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:122;s:8:"function";s:7:"ormLoad";s:5:"class";s:68:"Doctrine\Bundle\DoctrineBundle\DependencyInjection\DoctrineExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:2;a:6:{s:4:"type";i:16384;s:7:"message";s:197:"Since api-platform/core 3.3: The hydra: prefix will be removed in 4.0 by default, set "api_platform.serializer" or "serializer.default_context" to "hydra_prefix: true" to keep the current behavior.";s:4:"file";s:207:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\api-platform\core\src\Symfony\Bundle\DependencyInjection\ApiPlatformExtension.php";s:4:"line";i:239;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:207:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\api-platform\core\src\Symfony\Bundle\DependencyInjection\ApiPlatformExtension.php";s:4:"line";i:174;s:8:"function";s:27:"registerCommonConfiguration";s:5:"class";s:67:"ApiPlatform\Symfony\Bundle\DependencyInjection\ApiPlatformExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:3;a:6:{s:4:"type";i:16384;s:7:"message";s:240:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\ApprenantController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:4;a:6:{s:4:"type";i:16384;s:7:"message";s:241:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\CertificatController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:5;a:6:{s:4:"type";i:16384;s:7:"message";s:240:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\DashboardController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:6;a:6:{s:4:"type";i:16384;s:7:"message";s:241:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\DiagnosticController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:7;a:6:{s:4:"type";i:16384;s:7:"message";s:241:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\EvaluationController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:8;a:6:{s:4:"type";i:16384;s:7:"message";s:247:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\EvaluationDetailController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:9;a:6:{s:4:"type";i:16384;s:7:"message";s:240:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\EvenementController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:10;a:6:{s:4:"type";i:16384;s:7:"message";s:240:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\FormateurController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:11;a:6:{s:4:"type";i:16384;s:7:"message";s:241:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\MessagerieController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:12;a:6:{s:4:"type";i:16384;s:7:"message";s:243:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\NotificationController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:13;a:6:{s:4:"type";i:16384;s:7:"message";s:242:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\ProgressionController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:14;a:6:{s:4:"type";i:16384;s:7:"message";s:242:"Since symfony/security-bundle 6.2: The "Symfony\Component\Security\Core\Security" service alias is deprecated, use "Symfony\Bundle\SecurityBundle\Security" instead. It is being referenced by the "App\Controller\ReclamationController" service.";s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:67;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:198:"C:\Users\<USER>\OneDrive - Ministere de l'Enseignement Superieur et de la Recherche Scientifique\Bureau\pfe\backend_pfe\vendor\symfony\dependency-injection\Compiler\ResolveReferencesToAliasesPass.php";s:4:"line";i:50;s:8:"function";s:15:"getDefinitionId";s:5:"class";s:77:"Symfony\Component\DependencyInjection\Compiler\ResolveReferencesToAliasesPass";s:4:"type";s:2:"->";}}s:5:"count";i:1;}}
<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getProgressionControllerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the public 'App\Controller\ProgressionController' shared autowired service.
     *
     * @return \App\Controller\ProgressionController
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'framework-bundle'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AbstractController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'ProgressionController.php';

        $container->services['App\\Controller\\ProgressionController'] = $instance = new \App\Controller\ProgressionController(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), ($container->privates['App\\Repository\\ProgressionRepository'] ?? $container->load('getProgressionRepositoryService')), ($container->privates['App\\Repository\\ApprenantRepository'] ?? $container->load('getApprenantRepositoryService')), ($container->privates['App\\Repository\\CoursRepository'] ?? $container->load('getCoursRepositoryService')), ($container->privates['App\\Repository\\EvaluationRepository'] ?? $container->load('getEvaluationRepositoryService')), ($container->privates['App\\Repository\\QuizRepository'] ?? $container->load('getQuizRepositoryService')), ($container->privates['App\\Repository\\CertificatRepository'] ?? $container->load('getCertificatRepositoryService')), ($container->privates['security.helper'] ?? $container->load('getSecurity_HelperService')), ($container->privates['debug.serializer'] ?? self::getDebug_SerializerService($container)));

        $instance->setContainer(($container->privates['.service_locator.O2p6Lk7'] ?? $container->load('get_ServiceLocator_O2p6Lk7Service'))->withContext('App\\Controller\\ProgressionController', $container));

        return $instance;
    }
}

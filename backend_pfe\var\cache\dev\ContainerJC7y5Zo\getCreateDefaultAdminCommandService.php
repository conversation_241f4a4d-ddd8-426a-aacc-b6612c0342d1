<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getCreateDefaultAdminCommandService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Command\CreateDefaultAdminCommand' shared autowired service.
     *
     * @return \App\Command\CreateDefaultAdminCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'CreateDefaultAdminCommand.php';

        $container->privates['App\\Command\\CreateDefaultAdminCommand'] = $instance = new \App\Command\CreateDefaultAdminCommand(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), ($container->privates['App\\Repository\\AdministrateurRepository'] ?? $container->load('getAdministrateurRepositoryService')), ($container->privates['security.user_password_hasher'] ?? $container->load('getSecurity_UserPasswordHasherService')));

        $instance->setName('app:create-default-admin');
        $instance->setDescription('Creates a default admin user if none exists');

        return $instance;
    }
}

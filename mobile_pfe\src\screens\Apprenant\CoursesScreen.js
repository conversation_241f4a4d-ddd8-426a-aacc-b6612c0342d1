import React, { useState, useEffect, useContext } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  ScrollView,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { AuthContext } from "../../contexts/AuthContext";
import { ThemeContext } from "../../contexts/ThemeContext";
import coursService from "../../services/coursService";
import CourseCard from "../../components/CourseCard";
import LoadingScreen from "../../components/LoadingScreen";
import AppLayout from "../../components/AppLayout";
import AppButton from "../../components/AppButton";

const CoursesScreen = ({ navigation }) => {
  const { user } = useContext(AuthContext);
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [courses, setCourses] = useState([]);
  const [filteredCourses, setFilteredCourses] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState(null);
  const [activeFilter, setActiveFilter] = useState("all");

  const fetchCourses = async () => {
    try {
      setError(null);
      const coursesData = await coursService.getApprenantCours();

      // Fetch progression for each course
      const coursesWithProgress = [];
      for (const course of coursesData) {
        try {
          const progressionData =
            await coursService.getProgressionByApprenantAndCours(
              user.id,
              course.id
            );

          coursesWithProgress.push({
            ...course,
            progress_percentage: progressionData.progress_percentage || 0,
            quizzes_total: progressionData.quizzes_total || 0,
            quizzes_passed: progressionData.quizzes_passed || 0,
            is_completed: progressionData.is_completed || false,
            certificat: progressionData.certificat || null,
          });
        } catch (progressError) {
          console.error(
            `Error fetching progression for course ${course.id}:`,
            progressError
          );
          coursesWithProgress.push({
            ...course,
            progress_percentage: 0,
            quizzes_total: 0,
            quizzes_passed: 0,
            is_completed: false,
            certificat: null,
          });
        }
      }

      setCourses(coursesWithProgress);
      setFilteredCourses(coursesWithProgress);
    } catch (err) {
      console.error("Error fetching courses:", err);
      setError("Impossible de charger les cours");
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchCourses();
    setRefreshing(false);
  };

  useEffect(() => {
    const loadCourses = async () => {
      setLoading(true);
      await fetchCourses();
      setLoading(false);
    };

    loadCourses();
  }, []);

  useEffect(() => {
    if (courses.length > 0) {
      let filtered = [...courses];

      // Apply search filter
      if (searchQuery) {
        filtered = filtered.filter((course) =>
          (course.titre || course.title || "")
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
        );
      }

      // Apply category filter
      if (activeFilter !== "all") {
        if (activeFilter === "in-progress") {
          filtered = filtered.filter((course) => {
            const progress = course.progress_percentage || course.progress || 0;
            return progress > 0 && progress < 100;
          });
        } else if (activeFilter === "completed") {
          filtered = filtered.filter((course) => {
            const progress = course.progress_percentage || course.progress || 0;
            return progress === 100 || course.is_completed;
          });
        } else if (activeFilter === "not-started") {
          filtered = filtered.filter((course) => {
            const progress = course.progress_percentage || course.progress || 0;
            return progress === 0;
          });
        }
      }

      setFilteredCourses(filtered);
    }
  }, [searchQuery, activeFilter, courses]);

  const handleCoursePress = (course) => {
    navigation.navigate("CourseDetail", {
      courseId: course.id,
      title: course.titre || course.title,
    });
  };

  if (loading) {
    return <LoadingScreen message="Chargement des cours..." />;
  }

  const renderSearchBar = () => (
    <View style={styles.searchBarContainer}>
      <View
        style={[
          styles.searchContainer,
          { backgroundColor: isDarkMode ? theme.background : "#F3F4F6" },
        ]}
      >
        <MaterialCommunityIcons
          name="magnify"
          size={24}
          color={theme.text.secondary}
          style={styles.searchIcon}
        />
        <TextInput
          style={[styles.searchInput, { color: theme.text.primary }]}
          placeholder="Rechercher un cours..."
          placeholderTextColor={theme.text.tertiary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery("")}>
            <MaterialCommunityIcons
              name="close"
              size={20}
              color={theme.text.secondary}
            />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );

  const renderFilters = () => (
    <View style={styles.filtersContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filtersScrollContent}
      >
        <TouchableOpacity
          style={[
            styles.filterButton,
            { backgroundColor: isDarkMode ? theme.background : "#F3F4F6" },
            activeFilter === "all" && [
              styles.activeFilterButton,
              { backgroundColor: theme.primary },
            ],
          ]}
          onPress={() => setActiveFilter("all")}
        >
          <Text
            style={[
              styles.filterText,
              { color: theme.text.secondary },
              activeFilter === "all" && [
                styles.activeFilterText,
                { color: theme.text.inverse },
              ],
            ]}
          >
            Tous
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            { backgroundColor: isDarkMode ? theme.background : "#F3F4F6" },
            activeFilter === "in-progress" && [
              styles.activeFilterButton,
              { backgroundColor: theme.primary },
            ],
          ]}
          onPress={() => setActiveFilter("in-progress")}
        >
          <Text
            style={[
              styles.filterText,
              { color: theme.text.secondary },
              activeFilter === "in-progress" && [
                styles.activeFilterText,
                { color: theme.text.inverse },
              ],
            ]}
          >
            En cours
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            { backgroundColor: isDarkMode ? theme.background : "#F3F4F6" },
            activeFilter === "completed" && [
              styles.activeFilterButton,
              { backgroundColor: theme.primary },
            ],
          ]}
          onPress={() => setActiveFilter("completed")}
        >
          <Text
            style={[
              styles.filterText,
              { color: theme.text.secondary },
              activeFilter === "completed" && [
                styles.activeFilterText,
                { color: theme.text.inverse },
              ],
            ]}
          >
            Terminés
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.filterButton,
            { backgroundColor: isDarkMode ? theme.background : "#F3F4F6" },
            activeFilter === "not-started" && [
              styles.activeFilterButton,
              { backgroundColor: theme.primary },
            ],
          ]}
          onPress={() => setActiveFilter("not-started")}
        >
          <Text
            style={[
              styles.filterText,
              { color: theme.text.secondary },
              activeFilter === "not-started" && [
                styles.activeFilterText,
                { color: theme.text.inverse },
              ],
            ]}
          >
            Non commencés
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  const renderContent = () => {
    if (error) {
      return (
        <View style={styles.errorContainer}>
          <MaterialCommunityIcons
            name="alert-circle"
            size={48}
            color={theme.danger}
          />
          <Text style={[styles.errorText, { color: theme.text.primary }]}>
            {error}
          </Text>
          <AppButton
            title="Réessayer"
            onPress={onRefresh}
            variant="primary"
            size="medium"
          />
        </View>
      );
    }

    return (
      <FlatList
        data={filteredCourses}
        keyExtractor={(item) => item.id.toString()}
        renderItem={({ item }) => (
          <CourseCard course={item} onPress={handleCoursePress} />
        )}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons
              name="book-open-variant"
              size={48}
              color={theme.text.tertiary}
            />
            <Text style={[styles.emptyText, { color: theme.text.primary }]}>
              {searchQuery
                ? "Aucun cours ne correspond à votre recherche"
                : "Aucun cours disponible"}
            </Text>
            <Text
              style={[styles.emptySubtext, { color: theme.text.secondary }]}
            >
              {searchQuery
                ? "Essayez avec un autre terme de recherche"
                : "Les cours assignés apparaîtront ici"}
            </Text>
          </View>
        }
      />
    );
  };

  return (
    <AppLayout
      title="Mes cours"
      scrollable={false}
      refreshing={refreshing}
      onRefresh={onRefresh}
    >
      {renderSearchBar()}
      {renderFilters()}
      {renderContent()}
    </AppLayout>
  );
};

const styles = StyleSheet.create({
  searchBarContainer: {
    padding: 16,
    paddingTop: 0,
    paddingBottom: 8,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
  },
  filtersContainer: {
    paddingBottom: 8,
  },
  filtersScrollContent: {
    paddingHorizontal: 16,
    gap: 8,
    flexDirection: "row",
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  activeFilterButton: {
    // Background color set in component
  },
  filterText: {
    fontSize: 14,
  },
  activeFilterText: {
    fontWeight: "500",
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
    marginTop: 48,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: "600",
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: "center",
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    marginTop: 16,
    marginBottom: 16,
    textAlign: "center",
  },
});

export default CoursesScreen;

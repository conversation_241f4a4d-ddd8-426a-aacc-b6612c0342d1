<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getEmailServiceService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Service\EmailService' shared autowired service.
     *
     * @return \App\Service\EmailService
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'EmailService.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'mailer'.\DIRECTORY_SEPARATOR.'MailerInterface.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'mailer'.\DIRECTORY_SEPARATOR.'Mailer.php';

        $a = ($container->privates['mailer.transports'] ?? $container->load('getMailer_TransportsService'));

        if (isset($container->privates['App\\Service\\EmailService'])) {
            return $container->privates['App\\Service\\EmailService'];
        }
        $b = ($container->services['messenger.default_bus'] ?? self::getMessenger_DefaultBusService($container));

        if (isset($container->privates['App\\Service\\EmailService'])) {
            return $container->privates['App\\Service\\EmailService'];
        }
        $c = ($container->services['event_dispatcher'] ?? self::getEventDispatcherService($container));

        if (isset($container->privates['App\\Service\\EmailService'])) {
            return $container->privates['App\\Service\\EmailService'];
        }
        $d = ($container->privates['twig'] ?? self::getTwigService($container));

        if (isset($container->privates['App\\Service\\EmailService'])) {
            return $container->privates['App\\Service\\EmailService'];
        }

        return $container->privates['App\\Service\\EmailService'] = new \App\Service\EmailService(new \Symfony\Component\Mailer\Mailer($a, $b, $c), ($container->services['router'] ?? self::getRouterService($container)), $d, '<EMAIL>');
    }
}

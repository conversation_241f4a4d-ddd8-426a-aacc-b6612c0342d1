<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getFixQuizTypeCommand_LazyService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.App\Command\FixQuizTypeCommand.lazy' shared service.
     *
     * @return \Symfony\Component\Console\Command\LazyCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'LazyCommand.php';

        return $container->privates['.App\\Command\\FixQuizTypeCommand.lazy'] = new \Symfony\Component\Console\Command\LazyCommand('app:fix-quiz-type', [], 'Corrige le type de tous les quiz pour utiliser "Evaluation" au lieu de "formation" ou "Training"', false, #[\Closure(name: 'App\\Command\\FixQuizTypeCommand')] fn (): \App\Command\FixQuizTypeCommand => ($container->privates['App\\Command\\FixQuizTypeCommand'] ?? $container->load('getFixQuizTypeCommandService')));
    }
}

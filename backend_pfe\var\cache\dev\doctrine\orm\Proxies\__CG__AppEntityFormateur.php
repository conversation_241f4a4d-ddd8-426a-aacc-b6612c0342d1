<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Formateur extends \App\Entity\Formateur implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'evaluation' => [parent::class, 'evaluation', null, 16],
        "\0".parent::class."\0".'messenger' => [parent::class, 'messenger', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'currentPassword' => ['App\\Entity\\Utilisateur', 'currentPassword', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'email' => ['App\\Entity\\Utilisateur', 'email', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'id' => ['App\\Entity\\Utilisateur', 'id', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'isApproved' => ['App\\Entity\\Utilisateur', 'isApproved', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'name' => ['App\\Entity\\Utilisateur', 'name', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'newPassword' => ['App\\Entity\\Utilisateur', 'newPassword', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'password' => ['App\\Entity\\Utilisateur', 'password', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'phone' => ['App\\Entity\\Utilisateur', 'phone', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'profileImage' => ['App\\Entity\\Utilisateur', 'profileImage', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'resetToken' => ['App\\Entity\\Utilisateur', 'resetToken', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'resetTokenExpiresAt' => ['App\\Entity\\Utilisateur', 'resetTokenExpiresAt', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'role' => ['App\\Entity\\Utilisateur', 'role', null, 16],
        "\0".'App\\Entity\\Utilisateur'."\0".'roles' => ['App\\Entity\\Utilisateur', 'roles', null, 16],
        'currentPassword' => ['App\\Entity\\Utilisateur', 'currentPassword', null, 16],
        'email' => ['App\\Entity\\Utilisateur', 'email', null, 16],
        'evaluation' => [parent::class, 'evaluation', null, 16],
        'id' => ['App\\Entity\\Utilisateur', 'id', null, 16],
        'isApproved' => ['App\\Entity\\Utilisateur', 'isApproved', null, 16],
        'messenger' => [parent::class, 'messenger', null, 16],
        'name' => ['App\\Entity\\Utilisateur', 'name', null, 16],
        'newPassword' => ['App\\Entity\\Utilisateur', 'newPassword', null, 16],
        'password' => ['App\\Entity\\Utilisateur', 'password', null, 16],
        'phone' => ['App\\Entity\\Utilisateur', 'phone', null, 16],
        'profileImage' => ['App\\Entity\\Utilisateur', 'profileImage', null, 16],
        'resetToken' => ['App\\Entity\\Utilisateur', 'resetToken', null, 16],
        'resetTokenExpiresAt' => ['App\\Entity\\Utilisateur', 'resetTokenExpiresAt', null, 16],
        'role' => ['App\\Entity\\Utilisateur', 'role', null, 16],
        'roles' => ['App\\Entity\\Utilisateur', 'roles', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}

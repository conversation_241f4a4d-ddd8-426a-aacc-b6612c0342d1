// Simple test script to verify API connectivity
const axios = require('axios');

const API_URL = 'http://127.0.0.1:8001/api';

async function testAPI() {
  console.log('Testing API connection to:', API_URL);
  
  try {
    // Test basic connectivity
    const response = await axios.get(`${API_URL}/test`, {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    console.log('✅ API connection successful!');
    console.log('Response:', response.data);
    
    // Test login endpoint
    try {
      const loginResponse = await axios.post(`${API_URL}/login`, {
        email: '<EMAIL>',
        password: 'password'
      });
      console.log('✅ Login endpoint accessible');
    } catch (loginError) {
      console.log('ℹ️ Login endpoint responded (expected for invalid credentials):', loginError.response?.status);
    }
    
  } catch (error) {
    console.error('❌ API connection failed:');
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testAPI();

<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getApiPlatform_Validator_QueryParameterValidatorService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'api_platform.validator.query_parameter_validator' shared service.
     *
     * @return \ApiPlatform\ParameterValidator\ParameterValidator
     *
     * @deprecated Since api-platform/symfony 4.1: The "api_platform.validator.query_parameter_validator" service is deprecated use "\ApiPlatform\Metadata\Parameter::$constraints" instead.
     */
    public static function do($container, $lazyLoad = true)
    {
        trigger_deprecation('api-platform/symfony', '4.1', 'The "api_platform.validator.query_parameter_validator" service is deprecated use "\\ApiPlatform\\Metadata\\Parameter::$constraints" instead.');

        return new \ApiPlatform\ParameterValidator\ParameterValidator(($container->privates['api_platform.filter_locator'] ??= new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [], [])));
    }
}

<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerJC7y5Zo\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerJC7y5Zo/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerJC7y5Zo.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerJC7y5Zo\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerJC7y5Zo\App_KernelDevDebugContainer([
    'container.build_hash' => 'JC7y5Zo',
    'container.build_id' => '5af5384e',
    'container.build_time' => 1748799555,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerJC7y5Zo');

<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getTestEmailCommandService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Command\TestEmailCommand' shared autowired service.
     *
     * @return \App\Command\TestEmailCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'TestEmailCommand.php';

        $container->privates['App\\Command\\TestEmailCommand'] = $instance = new \App\Command\TestEmailCommand(($container->privates['App\\Service\\EmailService'] ?? $container->load('getEmailServiceService')));

        $instance->setName('app:test-email');
        $instance->setDescription('Test email sending functionality');

        return $instance;
    }
}

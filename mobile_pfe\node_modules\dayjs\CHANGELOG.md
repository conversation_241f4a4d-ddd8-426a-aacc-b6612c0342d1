## [1.11.13](https://github.com/iamkun/dayjs/compare/v1.11.12...v1.11.13) (2024-08-20)


### Bug Fixes

* customParseFormat supports Q quter / w ww weekOfYear ([#2705](https://github.com/iamkun/dayjs/issues/2705)) ([8ca74f1](https://github.com/iamkun/dayjs/commit/8ca74f178eff4bb4eb686676cf35fe7edb815536))

## [1.11.12](https://github.com/iamkun/dayjs/compare/v1.11.11...v1.11.12) (2024-07-18)


### Bug Fixes

* Add NegativeYear Plugin support  ([#2640](https://github.com/iamkun/dayjs/issues/2640)) ([6a42e0d](https://github.com/iamkun/dayjs/commit/6a42e0d7398639238f575d51287daaf4d495a2a3))
* add UTC support to negativeYear plugin ([#2692](https://github.com/iamkun/dayjs/issues/2692)) ([f3ef705](https://github.com/iamkun/dayjs/commit/f3ef705613af83333fe132b470896a65e12f31b0))
* Fix zero offset issue when use tz with locale ([#2532](https://github.com/iamkun/dayjs/issues/2532)) ([d0e6738](https://github.com/iamkun/dayjs/commit/d0e6738a66e1b65d3706aad2f9168ebb43d4f887))
* Improve typing for min/max plugin ([#2573](https://github.com/iamkun/dayjs/issues/2573)) ([4fbe94a](https://github.com/iamkun/dayjs/commit/4fbe94aaba8c815a42cf4d23dabac918ec50e68c))
* timezone plugin correct parse UTC tz ([#2693](https://github.com/iamkun/dayjs/issues/2693)) ([b575c81](https://github.com/iamkun/dayjs/commit/b575c81a8c9c85c7a0baf6f608a12f9d3ba95bd1))

## [1.11.11](https://github.com/iamkun/dayjs/compare/v1.11.10...v1.11.11) (2024-04-28)


### Bug Fixes

* day of week type literal ([#2630](https://github.com/iamkun/dayjs/issues/2630)) ([f68d73e](https://github.com/iamkun/dayjs/commit/f68d73efe562fdedd9e288ecb0ce6565e602f507))
* improve locale "zh-hk" format and meridiem ([#2419](https://github.com/iamkun/dayjs/issues/2419)) ([a947a51](https://github.com/iamkun/dayjs/commit/a947a5171aad5695eaf593bc95fe073de0f0894a))
* Update 'da' locale to match correct first week of year ([#2592](https://github.com/iamkun/dayjs/issues/2592)) ([44b0936](https://github.com/iamkun/dayjs/commit/44b0936ad709212b63e48672d8b9c225e2c3b830))
* update locale Bulgarian monthsShort Jan ([#2538](https://github.com/iamkun/dayjs/issues/2538)) ([f0c9a41](https://github.com/iamkun/dayjs/commit/f0c9a41c6ec91528f3790e442b0c5dff15a4e640))

## [1.11.10](https://github.com/iamkun/dayjs/compare/v1.11.9...v1.11.10) (2023-09-19)


### Bug Fixes

* Add Korean Day of Month with ordinal ([#2395](https://github.com/iamkun/dayjs/issues/2395)) ([dd55ee2](https://github.com/iamkun/dayjs/commit/dd55ee2aadd1009242235e47d558bbf028827896))
* change back fa locale to the Gregorian calendar equivalent ([#2411](https://github.com/iamkun/dayjs/issues/2411)) ([95e9458](https://github.com/iamkun/dayjs/commit/95e9458b221fe35e59ee4a160a5db247313a68fb))
* duration plugin - MILLISECONDS_A_MONTH const calculation ([#2362](https://github.com/iamkun/dayjs/issues/2362)) ([f0a0b54](https://github.com/iamkun/dayjs/commit/f0a0b546b074b3b511c2319a1ce83d412894b91f))
* duration plugin getter get result  0 instead of undefined ([#2369](https://github.com/iamkun/dayjs/issues/2369)) ([061aa7e](https://github.com/iamkun/dayjs/commit/061aa7ed6c31696974665fc9b11a74d30841ebed))
* fix isDayjs check logic ([#2383](https://github.com/iamkun/dayjs/issues/2383)) ([5f3f878](https://github.com/iamkun/dayjs/commit/5f3f8786c796cd432fe6bcb6966a810daea89203))
* fix timezone plugin to get correct locale setting ([#2420](https://github.com/iamkun/dayjs/issues/2420)) ([4f45012](https://github.com/iamkun/dayjs/commit/4f4501256fa1bc72128aae1d841bbd782df86aed))
* **locale:** add meridiem in `ar` locale ([#2418](https://github.com/iamkun/dayjs/issues/2418)) ([361be5c](https://github.com/iamkun/dayjs/commit/361be5c7c628614ee833d710acbe154a598b904d))
* round durations to millisecond precision for ISO string ([#2367](https://github.com/iamkun/dayjs/issues/2367)) ([890a17a](https://github.com/iamkun/dayjs/commit/890a17a8d8ddd43c7c8b806e3afc7b27f3288d27))
* sub-second precisions need to be rounded at the seconds field to avoid adding floats ([#2377](https://github.com/iamkun/dayjs/issues/2377)) ([a9d7d03](https://github.com/iamkun/dayjs/commit/a9d7d0398d22ebd4bfc3812ca0134a97606d54d9))
* update $x logic to avoid plugin error ([#2429](https://github.com/iamkun/dayjs/issues/2429)) ([2254635](https://github.com/iamkun/dayjs/commit/22546357f30924fcff3b3ffa14fd04be21f97a5e))
* Update Slovenian locale for relative time ([#2396](https://github.com/iamkun/dayjs/issues/2396)) ([5470a15](https://github.com/iamkun/dayjs/commit/5470a15e437fac803797363063b24f3ba3bd5299))
* update uzbek language translation ([#2327](https://github.com/iamkun/dayjs/issues/2327)) ([0a91056](https://github.com/iamkun/dayjs/commit/0a910564d76dc7c128da8e0d85d8e11ebdb5660b))

## [1.11.9](https://github.com/iamkun/dayjs/compare/v1.11.8...v1.11.9) (2023-07-01)


### Bug Fixes

* Add null to min and max plugin return type ([#2355](https://github.com/iamkun/dayjs/issues/2355)) ([62d9042](https://github.com/iamkun/dayjs/commit/62d9042eb84b78d78324694ccbeaad1679d37e68))
* check if null passed to objectSupport parser ([#2175](https://github.com/iamkun/dayjs/issues/2175)) ([013968f](https://github.com/iamkun/dayjs/commit/013968f609c32e2269df69b4dd1feb2e8e1e035a))
* dayjs.diff improve performance ([#2244](https://github.com/iamkun/dayjs/issues/2244)) ([33c80e1](https://github.com/iamkun/dayjs/commit/33c80e14cf14f70ceb4f54639e266cd70a3c3996))
* dayjs(null) throws error, not return dayjs object as invalid date ([#2334](https://github.com/iamkun/dayjs/issues/2334)) ([c79e2f5](https://github.com/iamkun/dayjs/commit/c79e2f5d03eef5660b1f13385b69c0c9668d2f98))
* objectSupport plugin causes an error when null is passed to dayjs function (closes [#2277](https://github.com/iamkun/dayjs/issues/2277)) ([#2342](https://github.com/iamkun/dayjs/issues/2342)) ([89bf31c](https://github.com/iamkun/dayjs/commit/89bf31ce0a36dcfc892029dc019d85d3654cf5fb))
* Optimize format method ([#2313](https://github.com/iamkun/dayjs/issues/2313)) ([1fe1b1d](https://github.com/iamkun/dayjs/commit/1fe1b1d9a214d3b8c9f267b432801424a493f1c4))
* update Duration plugin add/subtract take into account days in month ([#2337](https://github.com/iamkun/dayjs/issues/2337)) ([3b1060f](https://github.com/iamkun/dayjs/commit/3b1060f92183ab3a3c49289c2d87fbdd34c1eacc))
* update MinMax plugin 1. ignore the 'null' in args 2. return the only one arg ([#2330](https://github.com/iamkun/dayjs/issues/2330)) ([3c2c6ee](https://github.com/iamkun/dayjs/commit/3c2c6ee4db00bbb43a7a3bb0b56bc0d0f03daddc))

## [1.11.8](https://github.com/iamkun/dayjs/compare/v1.11.7...v1.11.8) (2023-06-02)


### Bug Fixes

* .format add padding to 'YYYY' ([#2231](https://github.com/iamkun/dayjs/issues/2231)) ([00c223b](https://github.com/iamkun/dayjs/commit/00c223b7e92970d07557133994fcb225a6d4c960))
* Added .valueOf method to Duration class ([#2226](https://github.com/iamkun/dayjs/issues/2226)) ([9b4fcfd](https://github.com/iamkun/dayjs/commit/9b4fcfde35b39693894be1821b6c7222fac98657))
* timezone type mark `date` parameter as optional ([#2222](https://github.com/iamkun/dayjs/issues/2222)) ([b87aa0e](https://github.com/iamkun/dayjs/commit/b87aa0ed9a748c478a66ef48230cd1d6350d7b8a))
* type file first parameter date is optional in isSame(), isBefore(), isAfter() ([#2272](https://github.com/iamkun/dayjs/issues/2272)) ([4d56f3e](https://github.com/iamkun/dayjs/commit/4d56f3eb2b3770879d60f824590bf1b32f237d47))

## [1.11.7](https://github.com/iamkun/dayjs/compare/v1.11.6...v1.11.7) (2022-12-06)


### Bug Fixes

* Add locale (zh-tw) meridiem ([#2149](https://github.com/iamkun/dayjs/issues/2149)) ([1e9ba76](https://github.com/iamkun/dayjs/commit/1e9ba761ff4e3f2759106dfe1aa9054d5826451c))
* update fa locale ([#2151](https://github.com/iamkun/dayjs/issues/2151)) ([1c26732](https://github.com/iamkun/dayjs/commit/1c267321a1a01b4947e1482bac67d67ebc7c3dfa))

## [1.11.6](https://github.com/iamkun/dayjs/compare/v1.11.5...v1.11.6) (2022-10-21)


### Bug Fixes

* add BigIntSupport plugin ([#2087](https://github.com/iamkun/dayjs/issues/2087)) ([f6dce48](https://github.com/iamkun/dayjs/commit/f6dce48a9e39677718b087867d9fd901d5078155))
* Fix objectSupport collides with Duration plugin - issue [#2027](https://github.com/iamkun/dayjs/issues/2027) ([#2038](https://github.com/iamkun/dayjs/issues/2038)) ([c9370ea](https://github.com/iamkun/dayjs/commit/c9370ea96bf420439ee7eaa4146e8ed643160312))

## [1.11.5](https://github.com/iamkun/dayjs/compare/v1.11.4...v1.11.5) (2022-08-12)


### Bug Fixes

* ordinal for nl not working ([#2011](https://github.com/iamkun/dayjs/issues/2011)) ([c93c85e](https://github.com/iamkun/dayjs/commit/c93c85eaa11564a1aae2d823480a417812c01bf4))
* wrong ordinal for french locale ([#2010](https://github.com/iamkun/dayjs/issues/2010)) ([dd192a7](https://github.com/iamkun/dayjs/commit/dd192a72fc5d26ce56481e89b0c1ccf5f939be0c))

## [1.11.4](https://github.com/iamkun/dayjs/compare/v1.11.3...v1.11.4) (2022-07-19)


### Bug Fixes

* correct past property in ku (kurdish) locale ([#1916](https://github.com/iamkun/dayjs/issues/1916)) ([74e82b9](https://github.com/iamkun/dayjs/commit/74e82b9da5ec8b90361fc27ac7c8b63faf354502))
* fix French [fr] local ordinal ([#1932](https://github.com/iamkun/dayjs/issues/1932)) ([8f09834](https://github.com/iamkun/dayjs/commit/8f09834a88b8e7f8353c6e7473d4711596890a8c))
* fix objectSupport plugin ConfigTypeMap type ([#1441](https://github.com/iamkun/dayjs/issues/1441)) ([#1990](https://github.com/iamkun/dayjs/issues/1990)) ([fd51fe4](https://github.com/iamkun/dayjs/commit/fd51fe4f7fa799d8c598343e71fa59299ec4cf93))
* fix type error to add ordianl property in InstanceLocaleDataReturn and GlobalLocaleDataReturn types ([#1931](https://github.com/iamkun/dayjs/issues/1931)) ([526f0ae](https://github.com/iamkun/dayjs/commit/526f0ae549ffbeeb9ef1099ca23964791fc59743))
* update locale ar-* meridiem function ([#1954](https://github.com/iamkun/dayjs/issues/1954)) ([3d31611](https://github.com/iamkun/dayjs/commit/3d316117f04362d31f4e8bd349620b8414ce5d0c))
* zh-tw / zh-hk locale ordinal error ([#1976](https://github.com/iamkun/dayjs/issues/1976)) ([0a1bd08](https://github.com/iamkun/dayjs/commit/0a1bd08e736be7d4e378aaca280caa6543f8066d))

## [1.11.3](https://github.com/iamkun/dayjs/compare/v1.11.2...v1.11.3) (2022-06-06)


### Bug Fixes

*  customParseFormat plugin to parse comma as a separator character ([#1913](https://github.com/iamkun/dayjs/issues/1913)) ([41b1405](https://github.com/iamkun/dayjs/commit/41b1405971e099431211ae6c2a100cd797da4427))
* update Dutch [nl] locale ordinal ([#1908](https://github.com/iamkun/dayjs/issues/1908)) ([5da98f8](https://github.com/iamkun/dayjs/commit/5da98f8085d2d2847d79e38c795082703a14f24b))

## [1.11.2](https://github.com/iamkun/dayjs/compare/v1.11.1...v1.11.2) (2022-05-06)


### Bug Fixes

* add OpUnitType (week) to quarterOfYear startOf/endOf types ([#1865](https://github.com/iamkun/dayjs/issues/1865)) ([400bc3e](https://github.com/iamkun/dayjs/commit/400bc3e8915e0c58e7abbfd3a1235364b1abaf3e))
* Fix type issue with ManipulateType ([#1864](https://github.com/iamkun/dayjs/issues/1864)) ([d033dfc](https://github.com/iamkun/dayjs/commit/d033dfcfc1d2ced39b2733898e8d85ad5984c9e9))
* fix UTC plugin .valueOf not taking DST into account  ([#1448](https://github.com/iamkun/dayjs/issues/1448)) ([27d1c50](https://github.com/iamkun/dayjs/commit/27d1c506100ae6624f258c21cc06b24768ced733))

## [1.11.1](https://github.com/iamkun/dayjs/compare/v1.11.0...v1.11.1) (2022-04-15)


### Bug Fixes

* add Bengali (Bangladesh) [bn-bd] locale ([#1806](https://github.com/iamkun/dayjs/issues/1806)) ([840ed76](https://github.com/iamkun/dayjs/commit/840ed76eedc085afefc4dedd05f31d44196b63b0))
* refactor replace deprecated String.prototype.substr() ([#1836](https://github.com/iamkun/dayjs/issues/1836)) ([627fa39](https://github.com/iamkun/dayjs/commit/627fa393e4daf83c92431162dbe18534b23fcbae))
* Update German [de] locale, adds the abbreviations for month including a . in the end, as in September -> Sept. ([#1831](https://github.com/iamkun/dayjs/issues/1831)) ([4e2802c](https://github.com/iamkun/dayjs/commit/4e2802cc3bec2941ffb737a15fb531c90951eafe))
* update Italian (Switzerland) [it-ch] locale relativeTime ([#1829](https://github.com/iamkun/dayjs/issues/1829)) ([8e6d11d](https://github.com/iamkun/dayjs/commit/8e6d11d053393d97bee1ba411adb2d82de1a58c4))
* update Kurdish [ku] locale strings and formatted output contains non-standard kurdish characters ([#1848](https://github.com/iamkun/dayjs/issues/1848)) ([a597d0b](https://github.com/iamkun/dayjs/commit/a597d0b1b8dd28e626f8c59d326622088f7b51e7))
* update locale bo [Tibetan]: corrected the orders in formats ([#1823](https://github.com/iamkun/dayjs/issues/1823)) ([e790516](https://github.com/iamkun/dayjs/commit/e79051617af6787358f6c9b5443d987b8b53a9e1))

# [1.11.0](https://github.com/iamkun/dayjs/compare/v1.10.8...v1.11.0) (2022-03-14)


### Bug Fixes

* Add Kirundi (rn) locale ([#1793](https://github.com/iamkun/dayjs/issues/1793)) ([74e5247](https://github.com/iamkun/dayjs/commit/74e5247227a779fffde39bdfcd1ee19911496709))
* add missing date shorthand D type definition ([#1752](https://github.com/iamkun/dayjs/issues/1752)) ([b045baf](https://github.com/iamkun/dayjs/commit/b045baf1646a81f7e4f446f355d02d5fb0ef4aa7))
* Add relative time to Galician (gl) and fix ordinals ([#1800](https://github.com/iamkun/dayjs/issues/1800)) ([dcbf170](https://github.com/iamkun/dayjs/commit/dcbf1708400624addfbddbc71e0f6a9ac15fa961))
* update German locales (de-at, de-ch) ([#1775](https://github.com/iamkun/dayjs/issues/1775)) ([f9055a7](https://github.com/iamkun/dayjs/commit/f9055a77bf3d84c575e5fcf99e21611138ba64d7))
* update Icelandic [is] locale relativeTime config ([#1796](https://github.com/iamkun/dayjs/issues/1796)) ([76f9e17](https://github.com/iamkun/dayjs/commit/76f9e1756de7e99c01e471dab30ea074b9ec9629))
* Update index.d.ts note ([#1716](https://github.com/iamkun/dayjs/issues/1716)) ([5a108ff](https://github.com/iamkun/dayjs/commit/5a108ff3159c53fd270ea7638f33c35c934d6457))
* Update locale German [de] monthsShort ([#1746](https://github.com/iamkun/dayjs/issues/1746)) ([4a7b7d0](https://github.com/iamkun/dayjs/commit/4a7b7d07c885bb9338514c234dbb708e24e9863e))
* update meridiem function to Kurdish (ku) locale ([#1725](https://github.com/iamkun/dayjs/issues/1725)) ([efd3904](https://github.com/iamkun/dayjs/commit/efd3904ff8cbf0a4fc064911dda76fc86b669f7b))
* update updateLocal plugin typescript types ([#1692](https://github.com/iamkun/dayjs/issues/1692)) ([c7a3f73](https://github.com/iamkun/dayjs/commit/c7a3f73064dbb63b4d365b2ad4c792f075f4d8d8))


### Features

* Fallback to language only locale + support uppercase locales ([#1524](https://github.com/iamkun/dayjs/issues/1524)) ([9138dc2](https://github.com/iamkun/dayjs/commit/9138dc28206875372da4fb74c64716437cd11b95))

## [1.10.8](https://github.com/iamkun/dayjs/compare/v1.10.7...v1.10.8) (2022-02-28)


### Bug Fixes

* set locale pt, pt-br correct weekdays and months ([#1697](https://github.com/iamkun/dayjs/issues/1697)) ([e019301](https://github.com/iamkun/dayjs/commit/e01930171c8235f58a114236f146086428f99569))

## [1.10.7](https://github.com/iamkun/dayjs/compare/v1.10.6...v1.10.7) (2021-09-10)


### Bug Fixes

* Add  Spanish (Mexico) [es-mx] locale ([#1614](https://github.com/iamkun/dayjs/issues/1614)) ([3393f2a](https://github.com/iamkun/dayjs/commit/3393f2ad55346d55902683a2e31c6f253d96c8c2))
* Add Arabic (Iraq) [ar-iq] locale ([#1627](https://github.com/iamkun/dayjs/issues/1627)) ([b5a1391](https://github.com/iamkun/dayjs/commit/b5a1391011b247d08863d291542db5937b23b427))
* add format object type to type file ([#1572](https://github.com/iamkun/dayjs/issues/1572)) ([5a79cc6](https://github.com/iamkun/dayjs/commit/5a79cc6408e825d9e123629eb44fc19c996d7751))
* duration plugin when parsing duration from ISO string, set missing components to 0 instead of NaN ([#1611](https://github.com/iamkun/dayjs/issues/1611)) ([252585b](https://github.com/iamkun/dayjs/commit/252585b4b2bd59508150e21bb994908a9d78f9b0))
* narrow type for `add` and `subtract` ([#1576](https://github.com/iamkun/dayjs/issues/1576)) ([1686962](https://github.com/iamkun/dayjs/commit/16869621b1a42563064dbf87f80c1ebfd74c1188))
* update customParseFormat plugin strict x X parsing ([#1571](https://github.com/iamkun/dayjs/issues/1571)) ([08adda5](https://github.com/iamkun/dayjs/commit/08adda54edbcca38601f57841921d0f87f84e49e))
* update Lithuanian [lt] locale spelling for single month ([#1609](https://github.com/iamkun/dayjs/issues/1609)) ([255dc54](https://github.com/iamkun/dayjs/commit/255dc54d9295de135a9037ce6ca13cae4bfd2cfb))
* Update Norwegian Bokmål [nb] local yearStart 4 ([#1608](https://github.com/iamkun/dayjs/issues/1608)) ([7a8467c](https://github.com/iamkun/dayjs/commit/7a8467c0b7d59821f7e19d4a6973bcda8e4c19b1))
* update plugin advancedFormat `isValid` validation ([#1566](https://github.com/iamkun/dayjs/issues/1566)) ([755fc8b](https://github.com/iamkun/dayjs/commit/755fc8bb1c532eb991459f180eee81367d12016c))
* update Sinhalese [si] locale month name ([#1475](https://github.com/iamkun/dayjs/issues/1475)) ([63de2a8](https://github.com/iamkun/dayjs/commit/63de2a8b7dcd7e68c132c85d88572d4c9d296907))
* update utcOffset plugin type file ([#1604](https://github.com/iamkun/dayjs/issues/1604)) ([f68e4b1](https://github.com/iamkun/dayjs/commit/f68e4b1a29fc33542f74cde10ec6d9fb045ca37e))

## [1.10.6](https://github.com/iamkun/dayjs/compare/v1.10.5...v1.10.6) (2021-07-06)


### Bug Fixes

* add invalid date string override ([#1465](https://github.com/iamkun/dayjs/issues/1465)) ([#1470](https://github.com/iamkun/dayjs/issues/1470)) ([06f88f4](https://github.com/iamkun/dayjs/commit/06f88f425828b1ce96b737332d25145a95a4ee9d))
* add sv-fi Finland Swedish locale ([#1522](https://github.com/iamkun/dayjs/issues/1522)) ([8e32164](https://github.com/iamkun/dayjs/commit/8e32164855cff724642e24c37a631eb4c4d760c8))
* customParseFormat support parsing X x timestamp ([#1567](https://github.com/iamkun/dayjs/issues/1567)) ([eb087f5](https://github.com/iamkun/dayjs/commit/eb087f52861313b8dd8a5c1b77858665ec72859e))
* dayjs ConfigTypeMap add null & undefined ([#1560](https://github.com/iamkun/dayjs/issues/1560)) ([b5e40e6](https://github.com/iamkun/dayjs/commit/b5e40e6f16abeaea6a0facfa466d20aefaa8a444))
* Fix DayOfYear plugin when using BadMutable plugin ([#1511](https://github.com/iamkun/dayjs/issues/1511)) ([0b0c6a3](https://github.com/iamkun/dayjs/commit/0b0c6a31ec9c0aff991b0e8dd6eed116201274cc))
* Implement ordinal in Bulgarian translation (fixes [#1501](https://github.com/iamkun/dayjs/issues/1501)) ([#1502](https://github.com/iamkun/dayjs/issues/1502)) ([b728da5](https://github.com/iamkun/dayjs/commit/b728da5ed9ed08210004ed20ce5fcd52a92de7da))
* more strict delimiter in REGEX_PARSE ([#1555](https://github.com/iamkun/dayjs/issues/1555)) ([bfdab5c](https://github.com/iamkun/dayjs/commit/bfdab5c0d45a5736b68e8e1b1354fc021e05f607))
* parameter type ([#1549](https://github.com/iamkun/dayjs/issues/1549)) ([f369844](https://github.com/iamkun/dayjs/commit/f369844dd69d253c4c7cbf68150939db3db233be))
* update customParseFormat plugin to custom two-digit year parse function ([#1421](https://github.com/iamkun/dayjs/issues/1421)) ([bb5df55](https://github.com/iamkun/dayjs/commit/bb5df55cd3975dc7638b8f4e762afa470b6620f7))
* update names of weekdays and months in Bulgarian [bg] to lowercase ([#1438](https://github.com/iamkun/dayjs/issues/1438)) ([b246210](https://github.com/iamkun/dayjs/commit/b24621091fec9cf6704de21e4b323f6f0c4abbf1))
* update type file `.diff` ([#1505](https://github.com/iamkun/dayjs/issues/1505)) ([6508494](https://github.com/iamkun/dayjs/commit/6508494a4e62977b4397baaeef293d1bcf3c7235))
* update UTC plugin type file for strict parsing ([#1443](https://github.com/iamkun/dayjs/issues/1443)) ([b4f28df](https://github.com/iamkun/dayjs/commit/b4f28df219fe63202dffdbeeaec5677c4d2c9111))

## [1.10.5](https://github.com/iamkun/dayjs/compare/v1.10.4...v1.10.5) (2021-05-26)


### Bug Fixes

* add meridiem in ar locales ([#1375](https://github.com/iamkun/dayjs/issues/1375)) ([319f616](https://github.com/iamkun/dayjs/commit/319f616e572a03b984013d04d1b3a18ffd5b1190))
* Added Zulu support to customParseFormat ([#1359](https://github.com/iamkun/dayjs/issues/1359)) ([1138a3f](https://github.com/iamkun/dayjs/commit/1138a3f0a76592c6d72fb86c4399e133fa41e2ec))
* fix Bengali [bn] locale monthsShort error ([a0e6c0c](https://github.com/iamkun/dayjs/commit/a0e6c0cf3e1828020dfa11432c6716990f6ed5e0))
* fix missing types for ArraySupport plugin ([#1401](https://github.com/iamkun/dayjs/issues/1401)) ([b1abdc4](https://github.com/iamkun/dayjs/commit/b1abdc40ee6c9d18ff46c311a114e0755677ea6f))
* fix Ukrainian [uk] locale ([#1463](https://github.com/iamkun/dayjs/issues/1463)) ([0fdac93](https://github.com/iamkun/dayjs/commit/0fdac93ff2531542301b76952be9b084b2e2dfa0))
* hotfix for `Duration` types ([#1357](https://github.com/iamkun/dayjs/issues/1357)) ([855b7b3](https://github.com/iamkun/dayjs/commit/855b7b3d049a3903794f91db3419f167c00dabd2)), closes [#1354](https://github.com/iamkun/dayjs/issues/1354)
* timezone plugin DST error ([#1352](https://github.com/iamkun/dayjs/issues/1352)) ([71bed15](https://github.com/iamkun/dayjs/commit/71bed155edf32bff24379930ac684fc783538d8f))
* Update duration plugin change string to number ([#1394](https://github.com/iamkun/dayjs/issues/1394)) ([e1546d1](https://github.com/iamkun/dayjs/commit/e1546d1a0cdb97ae92cf11efe61d94707af6a3a3))
* update Duration plugin to support no-argument ([#1400](https://github.com/iamkun/dayjs/issues/1400)) ([8d9a5ae](https://github.com/iamkun/dayjs/commit/8d9a5ae0749e1b4e76babd4deeaa3b1d9776c29b))
* Update Finnish [fi] locale to set yearStart  ([#1378](https://github.com/iamkun/dayjs/issues/1378)) ([f3370bd](https://github.com/iamkun/dayjs/commit/f3370bda4e435118f714c8a7daf5c88cfc4b69ba))
* update Russian [ru] locale meridiem and unit tests ([#1403](https://github.com/iamkun/dayjs/issues/1403)) ([f10f39d](https://github.com/iamkun/dayjs/commit/f10f39de7db70244a3c35e4a421090a12972457b))
* update Russian [ru] locale yearStart config  ([#1372](https://github.com/iamkun/dayjs/issues/1372)) ([5052515](https://github.com/iamkun/dayjs/commit/5052515fe35b2444201ef8ef87220b1876a94d0a))
* update Slovenian [sl] locale to set correct ordinal  ([#1386](https://github.com/iamkun/dayjs/issues/1386)) ([cb4f746](https://github.com/iamkun/dayjs/commit/cb4f74633b3020d6dbf19548c8cb13613dafca18))
* update Spanish [es] locale to change month names to lowercase ([#1414](https://github.com/iamkun/dayjs/issues/1414)) ([9c20e77](https://github.com/iamkun/dayjs/commit/9c20e77caf7b1b5eccf418175203b198d4e29535))
* update Swedish [sv] locale to set correct yearStart ([#1385](https://github.com/iamkun/dayjs/issues/1385)) ([66c5935](https://github.com/iamkun/dayjs/commit/66c59354964ef456bcd5f6152819618f44978082))
* update UTC plugin to support string argument like +HH:mm ([#1395](https://github.com/iamkun/dayjs/issues/1395)) ([656127c](https://github.com/iamkun/dayjs/commit/656127cc44eda50923a1ac755602863fc32b9e69))

## [1.10.4](https://github.com/iamkun/dayjs/compare/v1.10.3...v1.10.4) (2021-01-22)


### Bug Fixes

* Correct handling negative duration ([#1317](https://github.com/iamkun/dayjs/issues/1317)) ([3f5c085](https://github.com/iamkun/dayjs/commit/3f5c085608182472f20b84766b10949945663e44))
* Improve `Duration` types ([#1338](https://github.com/iamkun/dayjs/issues/1338)) ([4aca4b1](https://github.com/iamkun/dayjs/commit/4aca4b1b584a15de1146d929f95c944594032f20))
* parse a string for MMM month format with underscore delimiter ([#1349](https://github.com/iamkun/dayjs/issues/1349)) ([82ef9a3](https://github.com/iamkun/dayjs/commit/82ef9a304f06287ac0a14c4da9a7fe6152b5fec9))
* Update Bengali [bn] locale ([#1329](https://github.com/iamkun/dayjs/issues/1329)) ([02d96ec](https://github.com/iamkun/dayjs/commit/02d96ec7189f62d6ef8987135919cbb5ceff20a6))
* update locale Portuguese [pt] yearStart ([#1345](https://github.com/iamkun/dayjs/issues/1345)) ([5c785d5](https://github.com/iamkun/dayjs/commit/5c785d528cc08811638d7cbfc7fc158d67b32d75))
* update Polish [pl] locale yearStart ([#1348](https://github.com/iamkun/dayjs/issues/1348)) ([e93e6b8](https://github.com/iamkun/dayjs/commit/e93e6b8ffa61036b26382f1763e3864d4a7d5df5))
* Update Slovenian [sl] relativeTime locale ([#1333](https://github.com/iamkun/dayjs/issues/1333)) ([fe5f1d0](https://github.com/iamkun/dayjs/commit/fe5f1d0afbe57b70339e268047e6c3028ca3d59b))

## [1.10.3](https://github.com/iamkun/dayjs/compare/v1.10.2...v1.10.3) (2021-01-09)


### Bug Fixes

* fix customParseFormat plugin strict mode parse meridiem bug ([#1321](https://github.com/iamkun/dayjs/issues/1321)) ([e49eeef](https://github.com/iamkun/dayjs/commit/e49eeefbe8acb36419d36ca2e7ed8bc152f73ac1))
* fix weekYear plugin missing locale bug ([#1319](https://github.com/iamkun/dayjs/issues/1319)) ([344bdc0](https://github.com/iamkun/dayjs/commit/344bdc0eed6843edb05723dc7853a41833d88f08)), closes [#1304](https://github.com/iamkun/dayjs/issues/1304)
* update advancedFormat plugin to add format options for iso week and weekyear ([#1309](https://github.com/iamkun/dayjs/issues/1309)) ([2c54c64](https://github.com/iamkun/dayjs/commit/2c54c6441871a175ac9b95e41e4cd075dbac10cb))
* update devHelper to add dev warning setting locale before loading ([c5cc893](https://github.com/iamkun/dayjs/commit/c5cc89355e1e206ca72433c19c40cb528690b04f))
* update German [de] locale yearStart ([1858df8](https://github.com/iamkun/dayjs/commit/1858df8008de56570680723df89b36a8cbc970ef)), closes [#1264](https://github.com/iamkun/dayjs/issues/1264)

## [1.10.2](https://github.com/iamkun/dayjs/compare/v1.10.1...v1.10.2) (2021-01-05)


### Bug Fixes

* fix parse regex bug ([#1307](https://github.com/iamkun/dayjs/issues/1307)) ([db2b6a5](https://github.com/iamkun/dayjs/commit/db2b6a5ea8e70f9fda645d113ca33495aa96b616)), closes [#1305](https://github.com/iamkun/dayjs/issues/1305)
* remove module entry in package.json to revert 1.10.1 change ([#1314](https://github.com/iamkun/dayjs/issues/1314)) ([824dcb8](https://github.com/iamkun/dayjs/commit/824dcb8dfcccf14f64b6a2741a00fcdfe53dcd98))
* update devHelper add warning "passing Year as a Number will be parsed as a Unix timestamp" ([#1315](https://github.com/iamkun/dayjs/issues/1315)) ([b0dda31](https://github.com/iamkun/dayjs/commit/b0dda3139e25441ab4e7c1f4f192dee0ecce6ef8))

## [1.10.1](https://github.com/iamkun/dayjs/compare/v1.10.0...v1.10.1) (2021-01-03)


### Bug Fixes

* fix typescript type error UnitTypeLongPlural ([#1302](https://github.com/iamkun/dayjs/issues/1302)) ([bfaabe4](https://github.com/iamkun/dayjs/commit/bfaabe4f398c11564eca6cda7c8aded22e1b231a)), closes [#1300](https://github.com/iamkun/dayjs/issues/1300)

# [1.10.0](https://github.com/iamkun/dayjs/compare/v1.9.8...v1.10.0) (2021-01-03)


### Bug Fixes

* add ordinal to localeData plugin ([#1266](https://github.com/iamkun/dayjs/issues/1266)) ([fd229fa](https://github.com/iamkun/dayjs/commit/fd229fa5bd26bcba810e2535eb937ea8d99106c2))
* add preParsePostFormat plugin & update Arabic [ar] locale ([#1255](https://github.com/iamkun/dayjs/issues/1255)) ([f2e4790](https://github.com/iamkun/dayjs/commit/f2e479006a9a49bc0917f8620101d40ac645f7f2))
* add type support for plural forms of units ([#1289](https://github.com/iamkun/dayjs/issues/1289)) ([de49bb1](https://github.com/iamkun/dayjs/commit/de49bb100badfb92b9a5933cc568841f340a923f))
* escape last period to match only milliseconds ([#1239](https://github.com/iamkun/dayjs/issues/1239)) ([#1295](https://github.com/iamkun/dayjs/issues/1295)) ([64037e6](https://github.com/iamkun/dayjs/commit/64037e6a8cf303dcfd2b954f309bd9691f87fffc))


### Features

* add ES6 Module Support, package.json module point to "esm/index.js" ([#1298](https://github.com/iamkun/dayjs/issues/1298)) ([f63375d](https://github.com/iamkun/dayjs/commit/f63375dea89becbd3bb2bb8ea7289c58c752bfed)), closes [#598](https://github.com/iamkun/dayjs/issues/598) [#313](https://github.com/iamkun/dayjs/issues/313)

## [1.9.8](https://github.com/iamkun/dayjs/compare/v1.9.7...v1.9.8) (2020-12-27)


### Bug Fixes

* fix Ukrainian [uk] locale typo ([1605cc0](https://github.com/iamkun/dayjs/commit/1605cc0f6fe0e9c46a92d529bc9cd6e130432337))
* update Hebrew [he] locale for double units ([#1287](https://github.com/iamkun/dayjs/issues/1287)) ([1c4b0da](https://github.com/iamkun/dayjs/commit/1c4b0da1468522e59dc9ee646d10dd2b31477d99))
* update zh locale meridiem "noon" ([0e7ff3d](https://github.com/iamkun/dayjs/commit/0e7ff3dd29ca3aed85cb76dfcb8298d326e26542))
* update zh-cn locale definition of noon  ([#1278](https://github.com/iamkun/dayjs/issues/1278)) ([d5930b9](https://github.com/iamkun/dayjs/commit/d5930b96ff884f4176ca3fcb1bc95e8f1ec75c71))

## [1.9.7](https://github.com/iamkun/dayjs/compare/v1.9.6...v1.9.7) (2020-12-05)


### Bug Fixes

* add duration.format to format a Duration ([#1202](https://github.com/iamkun/dayjs/issues/1202)) ([9a859a1](https://github.com/iamkun/dayjs/commit/9a859a147ba223a1eeff0f2bb6f33d97e0ccc6c7))
* Add function handling for relativeTime.future and relativeTime.past ([#1197](https://github.com/iamkun/dayjs/issues/1197)) ([ef1979c](https://github.com/iamkun/dayjs/commit/ef1979ce85c61fe2d759ef3c37cb6aaf2358094f))
* avoid install installed plugin ([#1214](https://github.com/iamkun/dayjs/issues/1214)) ([a92eb6c](https://github.com/iamkun/dayjs/commit/a92eb6c4dc1437ec920e69484d52984f5921a8ea))
* avoid memory leak after installing a plugin too many times ([b8d2e32](https://github.com/iamkun/dayjs/commit/b8d2e32a9eb59661a7ed6200daa070687becaebd))
* fix diff bug when UTC plugin enabled ([#1201](https://github.com/iamkun/dayjs/issues/1201)) ([9544ed2](https://github.com/iamkun/dayjs/commit/9544ed2a6c466b8308d26b33a388a6737435a1f4)), closes [#1200](https://github.com/iamkun/dayjs/issues/1200)
* fix startOf/endOf bug in timezone plugin ([#1229](https://github.com/iamkun/dayjs/issues/1229)) ([eb5fbc4](https://github.com/iamkun/dayjs/commit/eb5fbc4c7d1b62a8615d2f263b404a9515d8e15c))
* fix utc plugin diff edge case ([#1187](https://github.com/iamkun/dayjs/issues/1187)) ([971b3d4](https://github.com/iamkun/dayjs/commit/971b3d40b4c9403165138f1034e2223cd97c3abf))
* update customParseFormat plugin to parse 2-digit offset ([#1209](https://github.com/iamkun/dayjs/issues/1209)) ([b56936a](https://github.com/iamkun/dayjs/commit/b56936ab77b8f6289a1b77d49307b495c4bf9f91)), closes [#1205](https://github.com/iamkun/dayjs/issues/1205)
* Update timezone plugin type definition ([#1221](https://github.com/iamkun/dayjs/issues/1221)) ([34cfb92](https://github.com/iamkun/dayjs/commit/34cfb920b9653ad44d4b31fe49e533692a3ce01b))

## [1.9.6](https://github.com/iamkun/dayjs/compare/v1.9.5...v1.9.6) (2020-11-10)


### Bug Fixes

* fix customParseFormat plugin parsing date bug ([#1198](https://github.com/iamkun/dayjs/issues/1198)) ([50f05ad](https://github.com/iamkun/dayjs/commit/50f05ad3addf27827c5657ae7519514e40d9faec)), closes [#1194](https://github.com/iamkun/dayjs/issues/1194)
* Update lv (Latvian) locale relative time ([#1192](https://github.com/iamkun/dayjs/issues/1192)) ([6d6c684](https://github.com/iamkun/dayjs/commit/6d6c6841b13ba4f7e69de92caf132a3592c5253a))

## [1.9.5](https://github.com/iamkun/dayjs/compare/v1.9.4...v1.9.5) (2020-11-05)


### Bug Fixes

* customParseFormat plugin supports parsing localizedFormats  ([#1110](https://github.com/iamkun/dayjs/issues/1110)) ([402b603](https://github.com/iamkun/dayjs/commit/402b603aa3ee4199786950bc88b3fdc6b527aa35))
* fix customParseFormat plugin parse meridiem bug ([#1169](https://github.com/iamkun/dayjs/issues/1169)) ([9e8f8d9](https://github.com/iamkun/dayjs/commit/9e8f8d96c69d557f4d267f42567c25ae9e7ab227)), closes [#1168](https://github.com/iamkun/dayjs/issues/1168)
* fix devHelper error in umd bundle in browser ([#1165](https://github.com/iamkun/dayjs/issues/1165)) ([d11b5ee](https://github.com/iamkun/dayjs/commit/d11b5ee7dc11af671355f65ccda00f6ba42cc725))
* fix utc plugin diff bug in DST ([#1171](https://github.com/iamkun/dayjs/issues/1171)) ([f8da3fe](https://github.com/iamkun/dayjs/commit/f8da3fe7e50c84c0502bf5be0b364910922dbd79)), closes [#1097](https://github.com/iamkun/dayjs/issues/1097) [#1021](https://github.com/iamkun/dayjs/issues/1021)
* isoWeek plugin type ([#1177](https://github.com/iamkun/dayjs/issues/1177)) ([c3d0436](https://github.com/iamkun/dayjs/commit/c3d0436b06f74989e3a2c751a5d170f8072c4aad))
* update localeData plugin to support meridiem ([#1174](https://github.com/iamkun/dayjs/issues/1174)) ([fdb09e4](https://github.com/iamkun/dayjs/commit/fdb09e4074cc7e8f6196846f18d3566c1f9e8fcd)), closes [#1172](https://github.com/iamkun/dayjs/issues/1172)
* update timezone plugin parse Date instance / timestamp logic & remove useless test ([#1183](https://github.com/iamkun/dayjs/issues/1183)) ([a7f858b](https://github.com/iamkun/dayjs/commit/a7f858bb70ad81f718ba35c479e84b54eace48b2))

## [1.9.4](https://github.com/iamkun/dayjs/compare/v1.9.3...v1.9.4) (2020-10-23)


### Bug Fixes

* Add descriptions to types ([#1148](https://github.com/iamkun/dayjs/issues/1148)) ([9a407a1](https://github.com/iamkun/dayjs/commit/9a407a140b089345a387d1aceab4d0d1635229c7))
* add devHelper plugin ([#1163](https://github.com/iamkun/dayjs/issues/1163)) ([de49dc8](https://github.com/iamkun/dayjs/commit/de49dc80c83b85de4170571b64412bd60ada221b))
* Fix Hungarian (hu) locale ([#1112](https://github.com/iamkun/dayjs/issues/1112)) ([ab13754](https://github.com/iamkun/dayjs/commit/ab13754f43c5033dacaa0eb2042dc4ab1a7a2754))
* fix minMax plugin parsing empty array bug ([#1062](https://github.com/iamkun/dayjs/issues/1062)) ([368108b](https://github.com/iamkun/dayjs/commit/368108bc6d5cb1542f711b8eba722bd4dfaab0cd))
* update adding/subtracting Duration from Dayjs object ([#1156](https://github.com/iamkun/dayjs/issues/1156)) ([f861aca](https://github.com/iamkun/dayjs/commit/f861acac3e83e28d3a4a96312c71119fd6b544fc))
* update en-NZ locale to use proper ordinal formatting function ([#1143](https://github.com/iamkun/dayjs/issues/1143)) ([fcdbc58](https://github.com/iamkun/dayjs/commit/fcdbc5880710456a29b2bacf250542230bf48b99))
* update localeData plugin type ([#1116](https://github.com/iamkun/dayjs/issues/1116)) ([ee5a4ec](https://github.com/iamkun/dayjs/commit/ee5a4ec41edddfb57d103c35182dc635c9264a10))
* update timezone plugin to support custom parse format ([#1160](https://github.com/iamkun/dayjs/issues/1160)) ([48cbf31](https://github.com/iamkun/dayjs/commit/48cbf3118ba5427de428777c2e025896db654f2e)), closes [#1159](https://github.com/iamkun/dayjs/issues/1159)
* update timezone plugin to support keepLocalTime ([#1161](https://github.com/iamkun/dayjs/issues/1161)) ([1d429e5](https://github.com/iamkun/dayjs/commit/1d429e5fe4467ebddcf81b43cf6f36e5e3be944c)), closes [#1149](https://github.com/iamkun/dayjs/issues/1149)

## [1.9.3](https://github.com/iamkun/dayjs/compare/v1.9.2...v1.9.3) (2020-10-13)


### Bug Fixes

* fix localizedFormat export error ([#1133](https://github.com/iamkun/dayjs/issues/1133)) ([deecd6a](https://github.com/iamkun/dayjs/commit/deecd6ab8a2f4173ee7046f6b568b41fd2677531)), closes [#1132](https://github.com/iamkun/dayjs/issues/1132)

## [1.9.2](https://github.com/iamkun/dayjs/compare/v1.9.1...v1.9.2) (2020-10-13)


### Bug Fixes

* add arraySupport plugin ([#1129](https://github.com/iamkun/dayjs/issues/1129)) ([be505c2](https://github.com/iamkun/dayjs/commit/be505c2c540261027342cecc55d8919a3d18d893))
* export type of duration plugin ([#1094](https://github.com/iamkun/dayjs/issues/1094)) ([2c92e71](https://github.com/iamkun/dayjs/commit/2c92e71bf55d09601120cdf433da7a19cc8abff6))
* Fix LocaleData plugin longDateFormat lowercase error ([#1101](https://github.com/iamkun/dayjs/issues/1101)) ([7937ccd](https://github.com/iamkun/dayjs/commit/7937ccdeac47d094a60e65ebb62a6020b81c46f4))
* Fix objectSupport plugin bug in UTC ([#1107](https://github.com/iamkun/dayjs/issues/1107)) ([fe90bb6](https://github.com/iamkun/dayjs/commit/fe90bb6944f2ff1969ca975954d303b449dfa95b)), closes [#1105](https://github.com/iamkun/dayjs/issues/1105)
* fix Serbian locale grammar (sr, sr-cyrl) ([#1108](https://github.com/iamkun/dayjs/issues/1108)) ([cc87eff](https://github.com/iamkun/dayjs/commit/cc87eff8b75b0d86ce0956516319d402bccae6c0))
* Fix typo for "monday" in arabic ([#1067](https://github.com/iamkun/dayjs/issues/1067)) ([2e1e426](https://github.com/iamkun/dayjs/commit/2e1e42650124f30282dc4d710798d576b928f1c7))
* support dayjs.add(Duration), dayjs.subtract(Duration) ([#1099](https://github.com/iamkun/dayjs/issues/1099)) ([b1a0294](https://github.com/iamkun/dayjs/commit/b1a02942c5238203aaa04ce9a074c73742324ab7))
* update Breton [br] locale relativeTime config ([#1103](https://github.com/iamkun/dayjs/issues/1103)) ([b038bfd](https://github.com/iamkun/dayjs/commit/b038bfdb128889d677c95534d2be29cc30c9e72f))
* update Catalan [ca] locale ordinal ([73da380](https://github.com/iamkun/dayjs/commit/73da38024c8b550bdcfbe3ff7e578e742c7aecf2))
* update German [de] locale relativeTime config ([#1109](https://github.com/iamkun/dayjs/issues/1109)) ([f6e771b](https://github.com/iamkun/dayjs/commit/f6e771b70f93d19ebb12e6b794aa4628a1796248))
* update localeData plugin to add longDateFormat to global localeData ([#1106](https://github.com/iamkun/dayjs/issues/1106)) ([16937d1](https://github.com/iamkun/dayjs/commit/16937d16e053b8c1d4a607622fa2fdbfd9809832))
* Update objectSupport plugin to return current date time while parsing empty object ([f56783e](https://github.com/iamkun/dayjs/commit/f56783e14d8cf50916b015e7188b23bb6fbca839))

## [1.9.1](https://github.com/iamkun/dayjs/compare/v1.9.0...v1.9.1) (2020-09-28)


### Bug Fixes

* Fix objectSupport plugin to get the correct result (zero-based month) ([#1089](https://github.com/iamkun/dayjs/issues/1089)) ([f95ac15](https://github.com/iamkun/dayjs/commit/f95ac15a4577ae5a3d1ce353872a2cd9fc454bc2))

# [1.9.0](https://github.com/iamkun/dayjs/compare/v1.8.36...v1.9.0) (2020-09-28)


### Bug Fixes

* Add `setDefault` typing to timezone.d.ts ([#1057](https://github.com/iamkun/dayjs/issues/1057)) ([c0f0886](https://github.com/iamkun/dayjs/commit/c0f088620f17260e6e3ebce7697d561b5623f5f3))
* fix DST bug in utc plugin ([#1053](https://github.com/iamkun/dayjs/issues/1053)) ([3d73543](https://github.com/iamkun/dayjs/commit/3d7354361f042ced1176d91f9ae9edffe6173425))
* Fix optional type for timezone plugin ([#1081](https://github.com/iamkun/dayjs/issues/1081)) ([a6ebcf2](https://github.com/iamkun/dayjs/commit/a6ebcf283a83273562dce5663155e3b3a12ea9a5)), closes [#1079](https://github.com/iamkun/dayjs/issues/1079)
* Fix timezone plugin conversion bug ([#1073](https://github.com/iamkun/dayjs/issues/1073)) ([16816a3](https://github.com/iamkun/dayjs/commit/16816a31ff43220aca9d1d179df6b729182abb55))
* update duration plugin type file ([#1065](https://github.com/iamkun/dayjs/issues/1065)) ([94af9af](https://github.com/iamkun/dayjs/commit/94af9af27c5bc182cbb24f1845e561dd1d82d776))
* update timezone plugin to support getting offset name e.g. EST ([#1069](https://github.com/iamkun/dayjs/issues/1069)) ([cbb755e](https://github.com/iamkun/dayjs/commit/cbb755e5c68d49c5678291f3ce832b32831a056e))
* update utc plugin to support keepLocalTime `.utc(true)` ([#1080](https://github.com/iamkun/dayjs/issues/1080)) ([5ce4e0d](https://github.com/iamkun/dayjs/commit/5ce4e0d2f552f3645262537ff7afdc946f5a7e72))


### Features

* Correct casing for en-sg locale name ([#1048](https://github.com/iamkun/dayjs/issues/1048)) ([2edaddc](https://github.com/iamkun/dayjs/commit/2edaddc22a7eb914f915531f389766217acd7034))

## [1.8.36](https://github.com/iamkun/dayjs/compare/v1.8.35...v1.8.36) (2020-09-17)


### Bug Fixes

* Add Amharic (am) locale ([#1046](https://github.com/iamkun/dayjs/issues/1046)) ([cdc49a1](https://github.com/iamkun/dayjs/commit/cdc49a1911c74b7ea96ed222f42796d53715cfed))
* Export Duration type in duration plugin ([#1043](https://github.com/iamkun/dayjs/issues/1043)) ([0f20c3a](https://github.com/iamkun/dayjs/commit/0f20c3ac75d9ac1026a15a7bb343d3a150d9b30f))
* Fix duration plugin parsing milliseconds bug ([#1042](https://github.com/iamkun/dayjs/issues/1042)) ([fe2301b](https://github.com/iamkun/dayjs/commit/fe2301b22318886aaa89ed1620e0a118e98c2b8a))
* Timezone plugin set default timezone ([#1033](https://github.com/iamkun/dayjs/issues/1033)) ([0c2050a](https://github.com/iamkun/dayjs/commit/0c2050a152da708b01edd6150a5013f642b14576))
* Timezone plugin should have the same behavior in latest ICU version ([#1032](https://github.com/iamkun/dayjs/issues/1032)) ([de31592](https://github.com/iamkun/dayjs/commit/de315921575cc50c38464b27d0338e30a54d8e2a))
* Update Finnish (fi) locale ([#963](https://github.com/iamkun/dayjs/issues/963)) ([cf8b6a0](https://github.com/iamkun/dayjs/commit/cf8b6a096f24b54cbdb95675ac386d8ac85ea616))
* Update Polish (pl) , Hungarian (hr) and Lithuanian (lt) localization ([#1045](https://github.com/iamkun/dayjs/issues/1045)) ([638fd39](https://github.com/iamkun/dayjs/commit/638fd394fc24f4188390faf387da6b156e7c6320))

## [1.8.35](https://github.com/iamkun/dayjs/compare/v1.8.34...v1.8.35) (2020-09-02)


### Bug Fixes

* Fix BadMutable plugin bug in .diff ([#1023](https://github.com/iamkun/dayjs/issues/1023)) ([40ab6d9](https://github.com/iamkun/dayjs/commit/40ab6d9a53e8047cfca63c611c25dd045372d021))
* fix LocaleData plugin to support instance.weekdays() API ([#1019](https://github.com/iamkun/dayjs/issues/1019)) ([a09d259](https://github.com/iamkun/dayjs/commit/a09d259a407b81d1cb6bb5623fad551c775d8674)), closes [#1017](https://github.com/iamkun/dayjs/issues/1017)
* Update Dutch (nl) locale to set correct yearStart ([1533a2c](https://github.com/iamkun/dayjs/commit/1533a2cc1475270032da2d87b19fc3d62327e6e3))

## [1.8.34](https://github.com/iamkun/dayjs/compare/v1.8.33...v1.8.34) (2020-08-20)


### Bug Fixes

* Fix Timezone plugin to preserve milliseconds while changing timezone ([#1003](https://github.com/iamkun/dayjs/issues/1003)) ([5f446ed](https://github.com/iamkun/dayjs/commit/5f446eda770fa97e895c81a8195b3ba5d082cef0)), closes [#1002](https://github.com/iamkun/dayjs/issues/1002)
* support parsing unlimited decimals of millisecond ([#1010](https://github.com/iamkun/dayjs/issues/1010)) ([d1bdd36](https://github.com/iamkun/dayjs/commit/d1bdd36a56e3d1786523a180e3fc18068f609135)), closes [#544](https://github.com/iamkun/dayjs/issues/544)
* update Duration plugin to support global locale ([#1008](https://github.com/iamkun/dayjs/issues/1008)) ([1c49c83](https://github.com/iamkun/dayjs/commit/1c49c83e79811eede13db6372b5d65db598aee77)), closes [#1007](https://github.com/iamkun/dayjs/issues/1007)

## [1.8.33](https://github.com/iamkun/dayjs/compare/v1.8.32...v1.8.33) (2020-08-10)


### Bug Fixes

* Add PluralGetSet plugin for plural getters/setters ([#996](https://github.com/iamkun/dayjs/issues/996)) ([f76e3ce](https://github.com/iamkun/dayjs/commit/f76e3ce2fbe5d3e9ed9121086baf55eb0cc4d355))
* Add typescript type defs in esm build ([#985](https://github.com/iamkun/dayjs/issues/985)) ([50e3b3c](https://github.com/iamkun/dayjs/commit/50e3b3c6719cb0b4ec6eff394dacd63d5db8f253))
* Fix isoWeek Plugin cal bug in UTC mode ([#993](https://github.com/iamkun/dayjs/issues/993)) ([f2e5f32](https://github.com/iamkun/dayjs/commit/f2e5f327aaf12b4572296ec6e107ecc05fcf76e7))
* Fix Timezone plugin parsing js date, Day.js object, timestamp bug && update type file ([#994](https://github.com/iamkun/dayjs/issues/994)) ([22f3d49](https://github.com/iamkun/dayjs/commit/22f3d49405da98db6da56d1673eebcd01b57554b)), closes [#992](https://github.com/iamkun/dayjs/issues/992) [#989](https://github.com/iamkun/dayjs/issues/989)
* Fix Timezone plugin UTCOffset rounding bug ([#987](https://github.com/iamkun/dayjs/issues/987)) ([b07182b](https://github.com/iamkun/dayjs/commit/b07182bbdf5aef7f6bf1e88fcd38432e2b8ee465)), closes [#986](https://github.com/iamkun/dayjs/issues/986)
* Fix UTC plugin bug while comparing an utc instance to a local one ([#995](https://github.com/iamkun/dayjs/issues/995)) ([747c0fb](https://github.com/iamkun/dayjs/commit/747c0fb4eba6353755b5dad3417fd8d5a408c378))
* Update pt-br locale weekStart 0 ([#984](https://github.com/iamkun/dayjs/issues/984)) ([0f881c1](https://github.com/iamkun/dayjs/commit/0f881c18efb02b9d0ba7f76cba92bb504226fa95))

## [1.8.32](https://github.com/iamkun/dayjs/compare/v1.8.31...v1.8.32) (2020-08-04)


### Bug Fixes

* Add Experimental Timezone Plugin ([#974](https://github.com/iamkun/dayjs/issues/974)) ([e69caba](https://github.com/iamkun/dayjs/commit/e69caba1b0957241a855aa0ae38db899fa2c3795))
* fix parse date string error e.g. '2020/9/30' ([#980](https://github.com/iamkun/dayjs/issues/980)) ([231790d](https://github.com/iamkun/dayjs/commit/231790da62af0494732960c2c50d86ae9bf63ec6)), closes [#979](https://github.com/iamkun/dayjs/issues/979)
* update monthDiff function to get more accurate results ([19e8a7f](https://github.com/iamkun/dayjs/commit/19e8a7f2f7582b717f49d446822e39603694433c))
* Update UTC plugin to support keepLocalTime ([#973](https://github.com/iamkun/dayjs/issues/973)) ([9f488e5](https://github.com/iamkun/dayjs/commit/9f488e5aca92f0b4c2951459436829d79f86d8d7))

## [1.8.31](https://github.com/iamkun/dayjs/compare/v1.8.30...v1.8.31) (2020-07-29)


### Bug Fixes

* Rollback LocalePresetType to string ([#968](https://github.com/iamkun/dayjs/issues/968)) ([b342bd3](https://github.com/iamkun/dayjs/commit/b342bd3d84987d6c7587a0c4590d614fb0e670d7))
* Update Regex to parse 'YYYY' correctly ([#969](https://github.com/iamkun/dayjs/issues/969)) ([70c1239](https://github.com/iamkun/dayjs/commit/70c123990dcc6bd479fa2b5d7f9985127872a826))

## [1.8.30](https://github.com/iamkun/dayjs/compare/v1.8.29...v1.8.30) (2020-07-22)


### Bug Fixes

* Add Haitian Creole (ht) and Spanish Puerto Rico (es-pr) locale configs ([#958](https://github.com/iamkun/dayjs/issues/958)) ([b2642e2](https://github.com/iamkun/dayjs/commit/b2642e2d1f87734a34808c66e5176cb18bc0414d))
* Fix UTC plugin wrong hour bug while adding month or year  ([#957](https://github.com/iamkun/dayjs/issues/957)) ([28ae070](https://github.com/iamkun/dayjs/commit/28ae070024ff26685c88ce4cc8747307e86923c9))
* Update French (fr) locale to set correct yearStart ([14ab808](https://github.com/iamkun/dayjs/commit/14ab808a7b7e226f2eb2cbe894916a18ed5d967d)), closes [#956](https://github.com/iamkun/dayjs/issues/956)

## [1.8.29](https://github.com/iamkun/dayjs/compare/v1.8.28...v1.8.29) (2020-07-02)


### Bug Fixes

* Duration plugin supports parse ISO string with week (W) ([#950](https://github.com/iamkun/dayjs/issues/950)) ([f0fc12a](https://github.com/iamkun/dayjs/commit/f0fc12adadcab53fb0577ad8f5e2f1cf784fd8f5))
* LocaleData plugin supports locale order ([#938](https://github.com/iamkun/dayjs/issues/938)) ([62f429d](https://github.com/iamkun/dayjs/commit/62f429db73a0a069b1267231dea172b85f4b90e3)), closes [#936](https://github.com/iamkun/dayjs/issues/936)
* Update type definition to support array format ([#945](https://github.com/iamkun/dayjs/issues/945)) ([81d4740](https://github.com/iamkun/dayjs/commit/81d4740511d47e34f891b21afeb0449ef8a28688)), closes [#944](https://github.com/iamkun/dayjs/issues/944)
* Update type definition to support strict mode ([#951](https://github.com/iamkun/dayjs/issues/951)) ([8d54f3f](https://github.com/iamkun/dayjs/commit/8d54f3f7d4d161e72c767fa09699e70a2b3d681c))

## [1.8.28](https://github.com/iamkun/dayjs/compare/v1.8.27...v1.8.28) (2020-05-28)


### Bug Fixes

* Fix CustomParseFormat plugin month index error ([#918](https://github.com/iamkun/dayjs/issues/918)) ([fa2ec7f](https://github.com/iamkun/dayjs/commit/fa2ec7fcb980dcd2c7498dafe2f9ca2e52d735cf)), closes [#915](https://github.com/iamkun/dayjs/issues/915)
* Update Ukrainian (uk) locale monthFormat and monthStandalone ([#899](https://github.com/iamkun/dayjs/issues/899)) ([a08756e](https://github.com/iamkun/dayjs/commit/a08756e80bd1d7126fca28c5ad9e382613fc86c4))

## [1.8.27](https://github.com/iamkun/dayjs/compare/v1.8.26...v1.8.27) (2020-05-14)


### Bug Fixes

* Add Kinyarwanda (rw) locale ([#903](https://github.com/iamkun/dayjs/issues/903)) ([f355235](https://github.com/iamkun/dayjs/commit/f355235a836540d77880959fb1b614c87e9f7b3e))
* Add plugin objectSupport ([#887](https://github.com/iamkun/dayjs/issues/887)) ([52dfb13](https://github.com/iamkun/dayjs/commit/52dfb13a6b84f0a753cc5761192b92416f440961))
* Add Turkmen (tk) locale ([#893](https://github.com/iamkun/dayjs/issues/893)) ([a9ca8dc](https://github.com/iamkun/dayjs/commit/a9ca8dcbbd0964c5b9abb4e8a2d620c983cf091a))
* Fix CustomParseFormat plugin set locale error ([#896](https://github.com/iamkun/dayjs/issues/896)) ([8035c8a](https://github.com/iamkun/dayjs/commit/8035c8a760549b631252252718db3cdc4ab2f68f))
* Fix locale month function bug ([#908](https://github.com/iamkun/dayjs/issues/908)) ([bf347c3](https://github.com/iamkun/dayjs/commit/bf347c36e401f50727fb5afcc537497b54b90d6b))
* Update CustomParseFormat plugin to support Array formats ([#906](https://github.com/iamkun/dayjs/issues/906)) ([97856c6](https://github.com/iamkun/dayjs/commit/97856c603ef5fbbeb1cf8a42387479e56a77dbe8))

## [1.8.26](https://github.com/iamkun/dayjs/compare/v1.8.25...v1.8.26) (2020-04-30)


### Bug Fixes

* Fix Duration plugin `.toISOString` format bug ([#889](https://github.com/iamkun/dayjs/issues/889)) ([058d624](https://github.com/iamkun/dayjs/commit/058d624808fd2be024ae846bcb2e03885f39b556)), closes [#888](https://github.com/iamkun/dayjs/issues/888)
* Fix WeekOfYear plugin bug while using BadMutable plugin ([#884](https://github.com/iamkun/dayjs/issues/884)) ([2977438](https://github.com/iamkun/dayjs/commit/2977438458542573a4500e21f7ba5d1f8442960e))
* Update CustomParseFormat plugin strict mode ([#882](https://github.com/iamkun/dayjs/issues/882)) ([db642ac](https://github.com/iamkun/dayjs/commit/db642ac73e52e00d8c41546b2935c9e691cf66e0))
* Update RelativeTime plugin default config ([#883](https://github.com/iamkun/dayjs/issues/883)) ([0606f42](https://github.com/iamkun/dayjs/commit/0606f425aef8ccbfc3da3e43cba368130603b0cc))

## [1.8.25](https://github.com/iamkun/dayjs/compare/v1.8.24...v1.8.25) (2020-04-21)


### Bug Fixes

* Fix CustomParseFormat plugin of parsing only YYYY / YYYY-MM bug ([#873](https://github.com/iamkun/dayjs/issues/873)) ([3cea04d](https://github.com/iamkun/dayjs/commit/3cea04d33d54d44bbdd3d026b5c7f67ebf176116)), closes [#849](https://github.com/iamkun/dayjs/issues/849)
* Fix Duration plugin get seconds ([#867](https://github.com/iamkun/dayjs/issues/867)) ([62b092d](https://github.com/iamkun/dayjs/commit/62b092d9f9a3db5506ef01f798bdf211f163f53f))
* Fix type definition of locale ([9790b85](https://github.com/iamkun/dayjs/commit/9790b853e6113243a7f4a81dd12c6509e406a102))
* Fix UTC plugin startOf, endOf bug ([#872](https://github.com/iamkun/dayjs/issues/872)) ([4141084](https://github.com/iamkun/dayjs/commit/4141084ba96d35cadcda3f1e661bf1d0f6c8e4de)), closes [#809](https://github.com/iamkun/dayjs/issues/809) [#808](https://github.com/iamkun/dayjs/issues/808)

## [1.8.24](https://github.com/iamkun/dayjs/compare/v1.8.23...v1.8.24) (2020-04-10)


### Bug Fixes

* Add config option to RelativeTime plugin ([#851](https://github.com/iamkun/dayjs/issues/851)) ([bd24034](https://github.com/iamkun/dayjs/commit/bd24034b95bfc656024b75ef3f3c986708845fed))
* add Duration plugin ([#858](https://github.com/iamkun/dayjs/issues/858)) ([d568273](https://github.com/iamkun/dayjs/commit/d568273223199ca0497f238e2cc3a8d3dcf32d0f))
* Add en-in, en-tt locales ([#855](https://github.com/iamkun/dayjs/issues/855)) ([c39fb96](https://github.com/iamkun/dayjs/commit/c39fb96e2a9102c14b004c14a6c073af9d266f2f))
* add isToday, isTomorrow, isYesterday plugins ([#857](https://github.com/iamkun/dayjs/issues/857)) ([fc08ab6](https://github.com/iamkun/dayjs/commit/fc08ab68f8a28269802deeab9d6b0473b92cdc51))
* Add option callback to Calendar plugin ([#839](https://github.com/iamkun/dayjs/issues/839)) ([b25be90](https://github.com/iamkun/dayjs/commit/b25be9094325295310c8fc5e617fb058be8a5f68))
* Fix monthsShort for locale fr ([#862](https://github.com/iamkun/dayjs/issues/862)) ([d2de9a0](https://github.com/iamkun/dayjs/commit/d2de9a0b44b830038ed0094f79bfd40726311f2a))
* Update Breton locale (br) meridiem config ([#856](https://github.com/iamkun/dayjs/issues/856)) ([a2a6672](https://github.com/iamkun/dayjs/commit/a2a66720abb788a8f1cffbfd0929b35579f29c72))
* Update Ukrainian (uk) locale relative time ([#842](https://github.com/iamkun/dayjs/issues/842)) ([578bc1a](https://github.com/iamkun/dayjs/commit/578bc1a23c6e737783bbac3da12c0ed5d1edcf82))

## [1.8.23](https://github.com/iamkun/dayjs/compare/v1.8.22...v1.8.23) (2020-03-16)


### Bug Fixes

* Add Chinese (zh) locale ([f9b8945](https://github.com/iamkun/dayjs/commit/f9b89453166d8b53d33b1d7eefd9942022552e6e))
* Fix IsoWeek plugin typescript definition ([#828](https://github.com/iamkun/dayjs/issues/828)) ([30aab0c](https://github.com/iamkun/dayjs/commit/30aab0c7bce85dfac0ae208a891def30f88b5cb4))
* Update Arabic (ar) locale relative time ([#836](https://github.com/iamkun/dayjs/issues/836)) ([14044c6](https://github.com/iamkun/dayjs/commit/14044c6fda1229e3f0e5473d3f886bd79589b15f))
* Update Slovak (sk) locale,  Czech (cs) locale ([#833](https://github.com/iamkun/dayjs/issues/833)) ([f0d451f](https://github.com/iamkun/dayjs/commit/f0d451f795e9ebf752cd854d51b25b11de2343a3))
* Update Thai (th) locale relativeTime ([#826](https://github.com/iamkun/dayjs/issues/826)) ([63b7c03](https://github.com/iamkun/dayjs/commit/63b7c03a6dbb0507d60776e8bad6cccde3828b88)), closes [#816](https://github.com/iamkun/dayjs/issues/816)

## [1.8.22](https://github.com/iamkun/dayjs/compare/v1.8.21...v1.8.22) (2020-03-08)


### Bug Fixes

* Add IsoWeek plugin ([#811](https://github.com/iamkun/dayjs/issues/811)) ([28a2207](https://github.com/iamkun/dayjs/commit/28a2207ef9849afbac15dd29267b2e7a09cd3c16))
* Fix unsupported locale fallback to previous one ([#819](https://github.com/iamkun/dayjs/issues/819)) ([4868715](https://github.com/iamkun/dayjs/commit/48687152cf5bee6a4c1b8ceea4bda8b9bab9be10))

## [1.8.21](https://github.com/iamkun/dayjs/compare/v1.8.20...v1.8.21) (2020-02-26)


### Bug Fixes

* Set + Get accept 'D' as the short version of 'date' ([#795](https://github.com/iamkun/dayjs/issues/795)) ([523c038](https://github.com/iamkun/dayjs/commit/523c03880fa8bbad83214494ad02cd606cdb8b30))
* Update DayOfYear plugin type ([#799](https://github.com/iamkun/dayjs/issues/799)) ([5809652](https://github.com/iamkun/dayjs/commit/5809652e40245b7759827d9bf317abdcfa75a330))
* Update fi (Finnish) locale relativeTime ([#797](https://github.com/iamkun/dayjs/issues/797)) ([4a470fb](https://github.com/iamkun/dayjs/commit/4a470fbd6fef9e051727d0f26d53cc050b85935d))

## [1.8.20](https://github.com/iamkun/dayjs/compare/v1.8.19...v1.8.20) (2020-02-04)


### Bug Fixes

* Add Bislama Locale (bi) ([#780](https://github.com/iamkun/dayjs/issues/780)) ([9ac6ab4](https://github.com/iamkun/dayjs/commit/9ac6ab481bc883dd4ecc02caab12c8b2fc218a42))
* Fix weekOfYear plugin to support yearStart locale for better week number result ([#769](https://github.com/iamkun/dayjs/issues/769)) ([f00db36](https://github.com/iamkun/dayjs/commit/f00db36e70bc7beaca1abadeb30a9b1fbb3261ee))
* Update et (Estonian) locale relativeTime ([#790](https://github.com/iamkun/dayjs/issues/790)) ([d8e0f45](https://github.com/iamkun/dayjs/commit/d8e0f45f6cd2d5e5704b9797929227454c92d1a5))
* Update LocaleData plugin to support dayjs.localeData().weekdays() API ([287fed6](https://github.com/iamkun/dayjs/commit/287fed6db9eb4fd979b4861aca4dacbd32422533)), closes [#779](https://github.com/iamkun/dayjs/issues/779)
* Update LocaleData plugin to support dayjs.months dayjs.weekdays API ([144c2ae](https://github.com/iamkun/dayjs/commit/144c2ae6e15fbf89e3acd7c8cb9e237c5f6e1348)), closes [#779](https://github.com/iamkun/dayjs/issues/779)
* Update pl locale fusional config ([d372475](https://github.com/iamkun/dayjs/commit/d3724758bb27d5b17587b995ba14e7e80dcd1151))

## [1.8.19](https://github.com/iamkun/dayjs/compare/v1.8.18...v1.8.19) (2020-01-06)


### Bug Fixes

* Add UpdateLocale plugin to update a locale's properties ([#766](https://github.com/iamkun/dayjs/issues/766)) ([82ce2ba](https://github.com/iamkun/dayjs/commit/82ce2ba8d7e402e40f6d005d400eb5356a0b0633))
* Fix CustomParseFormat Plugin 'YYYY-MM' use first day of the month ([ba709ec](https://github.com/iamkun/dayjs/commit/ba709eca86a71ae648bc68bf67d9abdc229198d4)), closes [#761](https://github.com/iamkun/dayjs/issues/761)
* Fix CustomParseFormat Plugin to set correct locale ([66ce23f](https://github.com/iamkun/dayjs/commit/66ce23f2e18c5506e8f1a7ef20d3483a4df80087))
* Fix WeekOfYear Plugin wrong calender week number bug ([79b86db](https://github.com/iamkun/dayjs/commit/79b86dbbf3cfd3f1e2165b3d479a7061ad1b6925)), closes [#760](https://github.com/iamkun/dayjs/issues/760)
* Update RelativeTime plugin to support function to make additional processing ([#767](https://github.com/iamkun/dayjs/issues/767)) ([4bd9250](https://github.com/iamkun/dayjs/commit/4bd9250fbe7131e2fddfb5fa1b3350e8c2262ca9))
* Update ru, uk, cs locale to support relativeTime with plural ([3f080f7](https://github.com/iamkun/dayjs/commit/3f080f7d6bfdc4018cbb7c4d0112ff1ead4ef6b8))

## [1.8.18](https://github.com/iamkun/dayjs/compare/v1.8.17...v1.8.18) (2019-12-18)


### Bug Fixes

* Add missing locale type definition ([#716](https://github.com/iamkun/dayjs/issues/716)) ([cde5d0b](https://github.com/iamkun/dayjs/commit/cde5d0b91be7b2f5f3098de4aa0b9a4f0f28ea5c))
* Fix .locale() handel unsupported locale ([78ec173](https://github.com/iamkun/dayjs/commit/78ec173fcecc1299516ab7b44f4554d431b4b2fd))
* Update Italian locale (it) ([#727](https://github.com/iamkun/dayjs/issues/727)) ([5b53e98](https://github.com/iamkun/dayjs/commit/5b53e98c0a3ba0eb9573a9c77caeb907439be9e7))
* Update locale (fa) ([#733](https://github.com/iamkun/dayjs/issues/733)) ([9ad2e47](https://github.com/iamkun/dayjs/commit/9ad2e47e0569b23991bb0d5578f49c792c12df08))
* Update locale (zh-cn) ([#706](https://github.com/iamkun/dayjs/issues/706)) ([e31e544](https://github.com/iamkun/dayjs/commit/e31e54414fb90e1f54da13a117748ba37f52645d))
* Update locale (zh-cn) meridiem ([#735](https://github.com/iamkun/dayjs/issues/735)) ([15d1b81](https://github.com/iamkun/dayjs/commit/15d1b813e7faf5a1f9d1ea6fc673fd27ac49d8b1))
* Update LocaleData plugin to support dayjs().longDateFormat() ([#734](https://github.com/iamkun/dayjs/issues/734)) ([aa0f210](https://github.com/iamkun/dayjs/commit/aa0f210a1e3c4f6aba61c3b96f9eb445b43a33f0)), closes [#680](https://github.com/iamkun/dayjs/issues/680)
* Update Mongolian (mn) locale relativeTime ([#753](https://github.com/iamkun/dayjs/issues/753)) ([6d51435](https://github.com/iamkun/dayjs/commit/6d51435092c0c94d8e50256d3f0f058cdd15febe))
* Update Swedish locale (sv) fix ordinal error ([#745](https://github.com/iamkun/dayjs/issues/745)) ([49670d5](https://github.com/iamkun/dayjs/commit/49670d5ae31e4e21636cc5a8bfe35fef0f6d9e4a)), closes [#743](https://github.com/iamkun/dayjs/issues/743)

## [1.8.17](https://github.com/iamkun/dayjs/compare/v1.8.16...v1.8.17) (2019-11-06)


### Bug Fixes

* Fix set utcOffset in utc mode ([d148115](https://github.com/iamkun/dayjs/commit/d148115dad8f1a5afc0a64e9b8163dfeba4616b6))
* Update advancedFormat plugin to support w ww wo week tokens … ([#678](https://github.com/iamkun/dayjs/issues/678)) ([26cfa63](https://github.com/iamkun/dayjs/commit/26cfa63a524b803f7966dac5464f9cbf8f63387e)), closes [#676](https://github.com/iamkun/dayjs/issues/676)
* Update ka locale weekdays ([f8ca3d4](https://github.com/iamkun/dayjs/commit/f8ca3d4ba1d3cbe41613d3909c0627935a51a0c4))
* Update nb locale ([#679](https://github.com/iamkun/dayjs/issues/679)) ([1063b0e](https://github.com/iamkun/dayjs/commit/1063b0e1b5c19a1354d233cc0f21438e7073233a))
* Update Polish locale (pl)([#713](https://github.com/iamkun/dayjs/issues/713)) ([30d2f02](https://github.com/iamkun/dayjs/commit/30d2f026b47188833a4f44fee4bab52467d4a718))
* Update Ukrainian locale (uk) ([#710](https://github.com/iamkun/dayjs/issues/710)) ([360161c](https://github.com/iamkun/dayjs/commit/360161cac75f597fdd51d9d1ff138601282a1b4b))
* UTC plugin set utcOffset value ([#668](https://github.com/iamkun/dayjs/issues/668)) ([8877883](https://github.com/iamkun/dayjs/commit/88778838e71dd309e79cd1a8094d5bea36ca3390))

## [1.8.16](https://github.com/iamkun/dayjs/compare/v1.8.15...v1.8.16) (2019-08-27)


### Bug Fixes

* Fix relativeTime Plugin .FromNow() result error in UTC mode ([a385d5c](https://github.com/iamkun/dayjs/commit/a385d5c))
* Handle locale in WeekOfYear plugin ([#658](https://github.com/iamkun/dayjs/issues/658)) ([0e45b0a](https://github.com/iamkun/dayjs/commit/0e45b0a))
* LocaleData plugin returns all months and weekdays data when pas no argument ([#645](https://github.com/iamkun/dayjs/issues/645)) ([95e70b4](https://github.com/iamkun/dayjs/commit/95e70b4))
* Return null in toJSON if not valid ([#633](https://github.com/iamkun/dayjs/issues/633)) ([19affc8](https://github.com/iamkun/dayjs/commit/19affc8))
* Update Danish (da) locale ([#626](https://github.com/iamkun/dayjs/issues/626)) ([ac2ec77](https://github.com/iamkun/dayjs/commit/ac2ec77))
* Update Korean locale meridiem ([#642](https://github.com/iamkun/dayjs/issues/642)) ([b457146](https://github.com/iamkun/dayjs/commit/b457146))
* update Occitan locale Catalan locale ([#630](https://github.com/iamkun/dayjs/issues/630)) ([fef135e](https://github.com/iamkun/dayjs/commit/fef135e))
* update pt-br locale ([#628](https://github.com/iamkun/dayjs/issues/628)) ([ccf596d](https://github.com/iamkun/dayjs/commit/ccf596d))
* Update weekdaysShort to some locale files ([#643](https://github.com/iamkun/dayjs/issues/643)) ([cc1f15f](https://github.com/iamkun/dayjs/commit/cc1f15f))

## [1.8.15](https://github.com/iamkun/dayjs/compare/v1.8.14...v1.8.15) (2019-07-08)


### Bug Fixes

* Fix dayjs.locale() returns current global locale ([#602](https://github.com/iamkun/dayjs/issues/602)) ([790cd1a](https://github.com/iamkun/dayjs/commit/790cd1a))
* Fix incorrect Thai locale translation of July ([#607](https://github.com/iamkun/dayjs/issues/607)) ([43cbfd3](https://github.com/iamkun/dayjs/commit/43cbfd3))
* Lowercase french locale months and weekdays ([#615](https://github.com/iamkun/dayjs/issues/615)) ([e5a257c](https://github.com/iamkun/dayjs/commit/e5a257c))
* Type - Export Ls object to query all available locales ([#623](https://github.com/iamkun/dayjs/issues/623)) ([f6bfae0](https://github.com/iamkun/dayjs/commit/f6bfae0))
* Update  nb (Norsk Bokmål) locale ([#604](https://github.com/iamkun/dayjs/issues/604)) ([907f5c9](https://github.com/iamkun/dayjs/commit/907f5c9))
* Update types of `.diff` API ([#617](https://github.com/iamkun/dayjs/issues/617)) ([f0f43d2](https://github.com/iamkun/dayjs/commit/f0f43d2))

## [1.8.14](https://github.com/iamkun/dayjs/compare/v1.8.13...v1.8.14) (2019-05-07)


### Bug Fixes

* Fix `.format` API returns UTC offset when value is 0 bug ([b254964](https://github.com/iamkun/dayjs/commit/b254964))
* Fix QuarterOfYear plugin bug ([#591](https://github.com/iamkun/dayjs/issues/591)) ([434f774](https://github.com/iamkun/dayjs/commit/434f774))
* Fix UTC plugin add day DST bug ([#590](https://github.com/iamkun/dayjs/issues/590)) ([86cd839](https://github.com/iamkun/dayjs/commit/86cd839))

## [1.8.13](https://github.com/iamkun/dayjs/compare/v1.8.12...v1.8.13) (2019-04-26)


### Bug Fixes

* Add missing relativeTime and formats for some locales ([#560](https://github.com/iamkun/dayjs/issues/560)) ([96b917e](https://github.com/iamkun/dayjs/commit/96b917e))
* Add weekday (locale aware day of the week) plugin ([#569](https://github.com/iamkun/dayjs/issues/569)) ([9007cc5](https://github.com/iamkun/dayjs/commit/9007cc5)), closes [#559](https://github.com/iamkun/dayjs/issues/559)
* Allow customizing "am" / "pm" strings with locale meridiem function ([#580](https://github.com/iamkun/dayjs/issues/580)) ([576e93e](https://github.com/iamkun/dayjs/commit/576e93e)), closes [#578](https://github.com/iamkun/dayjs/issues/578)
* Fix `.add` day/week decimal rouding bug ([800f6c9](https://github.com/iamkun/dayjs/commit/800f6c9))
* Fix `.diff` type definition error ([#565](https://github.com/iamkun/dayjs/issues/565)) ([c4921ae](https://github.com/iamkun/dayjs/commit/c4921ae)), closes [#561](https://github.com/iamkun/dayjs/issues/561)
* Fix CustomParseFormat plugin bug ([#568](https://github.com/iamkun/dayjs/issues/568)) ([1f5a9db](https://github.com/iamkun/dayjs/commit/1f5a9db)), closes [#555](https://github.com/iamkun/dayjs/issues/555)
* Fix relativeTime plugin Math.round bug ([40bea40](https://github.com/iamkun/dayjs/commit/40bea40))
* skip square brackets in  buddhistEra, advancedFormat plugins ([#556](https://github.com/iamkun/dayjs/issues/556)) ([9279718](https://github.com/iamkun/dayjs/commit/9279718)), closes [#554](https://github.com/iamkun/dayjs/issues/554)
* Update Indonesian locale([#574](https://github.com/iamkun/dayjs/issues/574)) ([0aa7143](https://github.com/iamkun/dayjs/commit/0aa7143))
* Update locale month to support both array and function ([#581](https://github.com/iamkun/dayjs/issues/581)) ([b6599d3](https://github.com/iamkun/dayjs/commit/b6599d3))
* Update LocalizedFormat plugin lowercase formats logic ([#557](https://github.com/iamkun/dayjs/issues/557)) ([d409304](https://github.com/iamkun/dayjs/commit/d409304))

## [1.8.12](https://github.com/iamkun/dayjs/compare/v1.8.11...v1.8.12) (2019-04-02)


### Bug Fixes

* Add .get API ([7318797](https://github.com/iamkun/dayjs/commit/7318797))
* Add 79 locales ([#541](https://github.com/iamkun/dayjs/issues/541)) ([f75a125](https://github.com/iamkun/dayjs/commit/f75a125))
* Add Calendar plugin ([d1b9cf9](https://github.com/iamkun/dayjs/commit/d1b9cf9))
* Add isoWeeksInYear plugin ([2db8631](https://github.com/iamkun/dayjs/commit/2db8631))
* Add Occitan (oc-lnc) locale file ([#551](https://github.com/iamkun/dayjs/issues/551)) ([c30b715](https://github.com/iamkun/dayjs/commit/c30b715))
* Add plugin minMax to sopport .max .min ([2870a23](https://github.com/iamkun/dayjs/commit/2870a23))
* Fix set Month Year error in last day of the month ([d058f4a](https://github.com/iamkun/dayjs/commit/d058f4a))
* Update ko locale weekdaysShort  ([#543](https://github.com/iamkun/dayjs/issues/543)) ([317fd3e](https://github.com/iamkun/dayjs/commit/317fd3e))
* Update localizedFormat plugin to support lowercase localizable formats (l, ll, lll, llll) ([#546](https://github.com/iamkun/dayjs/issues/546)) ([f2b5ebf](https://github.com/iamkun/dayjs/commit/f2b5ebf))

## [1.8.11](https://github.com/iamkun/dayjs/compare/v1.8.10...v1.8.11) (2019-03-21)


### Bug Fixes

* Add .add('quarter') .startOf('quarter') through plugin quarterOfYear ([dde39e9](https://github.com/iamkun/dayjs/commit/dde39e9)), closes [#537](https://github.com/iamkun/dayjs/issues/537) [#531](https://github.com/iamkun/dayjs/issues/531)
* Add locale support for Azerbaijani language (az) ([#535](https://github.com/iamkun/dayjs/issues/535)) ([eeb20fa](https://github.com/iamkun/dayjs/commit/eeb20fa))
* Correct typescript definition `add` ([22a249c](https://github.com/iamkun/dayjs/commit/22a249c)), closes [#531](https://github.com/iamkun/dayjs/issues/531)
* Fix CustomParseFormat plugin formatting bug ([#536](https://github.com/iamkun/dayjs/issues/536)) ([8578546](https://github.com/iamkun/dayjs/commit/8578546)), closes [#533](https://github.com/iamkun/dayjs/issues/533)
* Update pt locale ([#538](https://github.com/iamkun/dayjs/issues/538)) ([1ac9e1e](https://github.com/iamkun/dayjs/commit/1ac9e1e))

## [1.8.10](https://github.com/iamkun/dayjs/compare/v1.8.9...v1.8.10) (2019-03-10)


### Bug Fixes

* **locale:** Add nepali (ne) locale ([#524](https://github.com/iamkun/dayjs/issues/524)) ([bdbec01](https://github.com/iamkun/dayjs/commit/bdbec01))
* Add WeekYear plugin ([a892608](https://github.com/iamkun/dayjs/commit/a892608))
* API .locale() with no argument should return current locale name string ([8d63d88](https://github.com/iamkun/dayjs/commit/8d63d88))
* CustomParseFormat correct parse HH:mm:ss with only one digit like 0:12:10 ([600d547](https://github.com/iamkun/dayjs/commit/600d547))
* CustomParseFormat plugin parse Do format string ([bf27fda](https://github.com/iamkun/dayjs/commit/bf27fda)), closes [#522](https://github.com/iamkun/dayjs/issues/522)
* Expand setters like .year(2000) .hour(12) ([ac532a0](https://github.com/iamkun/dayjs/commit/ac532a0))
* Move toObject, toArray API to separate plugin from core ([40a3431](https://github.com/iamkun/dayjs/commit/40a3431))

## [1.8.9](https://github.com/iamkun/dayjs/compare/v1.8.8...v1.8.9) (2019-03-06)


### Features

* Add UTC mode with UTC plugin ([#517](https://github.com/iamkun/dayjs/issues/517)) ([caf335c](https://github.com/iamkun/dayjs/commit/caf335c))

> For plugin developers: Please note, we have changed the name of some method in `Utils` in order to reduce the file size. ([#517](https://github.com/iamkun/dayjs/issues/517)) ([detail](https://github.com/iamkun/dayjs/pull/517/files#diff-2b4ca49d4bb0a774c4d4c1672d7aa781R46))

### Bug Fixes

* Add locale de-AT ([#515](https://github.com/iamkun/dayjs/issues/515)) ([d93f7b6](https://github.com/iamkun/dayjs/commit/d93f7b6))
* Add locale zh-hk ([#516](https://github.com/iamkun/dayjs/issues/516)) ([5fc05a6](https://github.com/iamkun/dayjs/commit/5fc05a6))

## [1.8.8](https://github.com/iamkun/dayjs/compare/v1.8.7...v1.8.8) (2019-02-25)


### Bug Fixes

* Update relativeTime plugin type definition ([de56f2c](https://github.com/iamkun/dayjs/commit/de56f2c))

## [1.8.7](https://github.com/iamkun/dayjs/compare/v1.8.6...v1.8.7) (2019-02-24)


### Bug Fixes

* Add plugin type definitions ([#418](https://github.com/iamkun/dayjs/issues/418)) ([361d437](https://github.com/iamkun/dayjs/commit/361d437))
* Add Swahili locale ([#508](https://github.com/iamkun/dayjs/issues/508)) ([b9cee84](https://github.com/iamkun/dayjs/commit/b9cee84))
* Parse month string 'MMMM MMM (February, Feb)' in customParseFormat ([#457](https://github.com/iamkun/dayjs/issues/457)) ([f343206](https://github.com/iamkun/dayjs/commit/f343206))
* Update declaration file .diff .isBefore .isSame .isAfter ([#496](https://github.com/iamkun/dayjs/issues/496)) ([4523275](https://github.com/iamkun/dayjs/commit/4523275))
* Word orders corrections for locale 'fa' ([#491](https://github.com/iamkun/dayjs/issues/491)) ([56050c2](https://github.com/iamkun/dayjs/commit/56050c2))

## [1.8.6](https://github.com/iamkun/dayjs/compare/v1.8.5...v1.8.6) (2019-02-14)


### Bug Fixes

* Add Bahasa Melayu (Malaysia) locale ([#485](https://github.com/iamkun/dayjs/issues/485)) ([cb208b0](https://github.com/iamkun/dayjs/commit/cb208b0))
* Copy & export built-in en locale to /locale folder as a separate file ([a7e05e0](https://github.com/iamkun/dayjs/commit/a7e05e0))
* Fix bug in customParseFormat plugin while month(MM) is '01' ([9884ca5](https://github.com/iamkun/dayjs/commit/9884ca5)), closes [#494](https://github.com/iamkun/dayjs/issues/494)
* Fix startOf week bug while week start is not Sunday ([5eaf77b](https://github.com/iamkun/dayjs/commit/5eaf77b))
* Implemented isBetween inclusivity ([#464](https://github.com/iamkun/dayjs/issues/464)) ([af2f4f1](https://github.com/iamkun/dayjs/commit/af2f4f1))
* Update Swedish and Finnish locales ([#488](https://github.com/iamkun/dayjs/issues/488)) ([f142082](https://github.com/iamkun/dayjs/commit/f142082))
* Fix commonJS require ES Module bug in webpack4 ([23f9f3d](https://github.com/iamkun/dayjs/commit/23f9f3d)), check [#492](https://github.com/iamkun/dayjs/issues/492)

> Get access to ESM code with `import dayjs from 'dayjs/esm'`

## [1.8.5](https://github.com/iamkun/dayjs/compare/v1.8.4...v1.8.5) (2019-02-07)


### Bug Fixes

* Add en-gb locale ([#478](https://github.com/iamkun/dayjs/issues/478)) ([508c3a7](https://github.com/iamkun/dayjs/commit/508c3a7))
* **module:** transpile everything except ES6 modules in the 'module' entrypoint ([#477](https://github.com/iamkun/dayjs/issues/477)) ([#480](https://github.com/iamkun/dayjs/issues/480)) ([#482](https://github.com/iamkun/dayjs/issues/482)) ([767017d](https://github.com/iamkun/dayjs/commit/767017d))
* update customParseFormat plugin support hh:mm ([54947cc](https://github.com/iamkun/dayjs/commit/54947cc)), closes [#484](https://github.com/iamkun/dayjs/issues/484)
* Update module in package.json ([5c5a7a0](https://github.com/iamkun/dayjs/commit/5c5a7a0))

## [1.8.4](https://github.com/iamkun/dayjs/compare/v1.8.3...v1.8.4) (2019-02-05)

* Allow set start day of week in locale && Allow set week in weekOfYear plugin ([1295591](https://github.com/iamkun/dayjs/commit/1295591))
### Bug Fixes
* update all locale files with correct week start ([5b03412](https://github.com/iamkun/dayjs/commit/5b03412))
* update es es-do locale adding weekStart && update weekStart test ([66e42ec](https://github.com/iamkun/dayjs/commit/66e42ec))
* Revert default export ([b00da1b](https://github.com/iamkun/dayjs/commit/b00da1b))

## [1.8.3](https://github.com/iamkun/dayjs/compare/v1.8.2...v1.8.3) (2019-02-04)


### Bug Fixes

* fix ios safari YYYY-MM-DD HH:mm parse BUG ([e02ae82](https://github.com/iamkun/dayjs/commit/e02ae82)), closes [#254](https://github.com/iamkun/dayjs/issues/254)

## [1.8.2](https://github.com/iamkun/dayjs/compare/v1.8.1...v1.8.2) (2019-02-02)


### Bug Fixes

* Add missing czech language locale ([#461](https://github.com/iamkun/dayjs/issues/461)) ([7e04004](https://github.com/iamkun/dayjs/commit/7e04004))
* Add utcOffset api method and fix calculating diff error in DST ([#453](https://github.com/iamkun/dayjs/issues/453)) ([ce2e30e](https://github.com/iamkun/dayjs/commit/ce2e30e))
* Fix it locale error ([#458](https://github.com/iamkun/dayjs/issues/458)) ([f6d9a64](https://github.com/iamkun/dayjs/commit/f6d9a64))
* Add DayOfYear plugin (#454)
* Fix es locale monthsShort error

## [1.8.1](https://github.com/iamkun/dayjs/compare/v1.8.0...v1.8.1) (2019-02-02)

* Add LocalizedFormat plugin supplying format like LTS, LT, LLLL 

* <del>update declaration File with default export (#278)</del>
> <del>From v1.8.1, in TypeScript Project, just `import from dayjs from 'dayjs'`</del>
* add ES2015 module support (#451)

### Performance Improvements

* **format:** reuse matches instead of created when replacing ([#441](https://github.com/iamkun/dayjs/issues/441)) ([10b79d8](https://github.com/iamkun/dayjs/commit/10b79d8))

# [1.8.0](https://github.com/iamkun/dayjs/compare/v1.7.8...v1.8.0) (2019-01-14)


### Features

* add CustomParseFormat plugin  and QuarterOfYear plugin ([#450](https://github.com/iamkun/dayjs/issues/450)) ([8f6f63c](https://github.com/iamkun/dayjs/commit/8f6f63c))

## [1.7.8](https://github.com/iamkun/dayjs/compare/v1.7.7...v1.7.8) (2018-12-13)


### Feature

* update isSame isBefore isAfter supports units ([fd65464](https://github.com/iamkun/dayjs/commit/fd65464))

* add greek lithuanian locales 

## [1.7.7](https://github.com/iamkun/dayjs/compare/v1.7.6...v1.7.7) (2018-09-26)


### Bug Fixes

* **DST:** fix daylight saving time DST bug && add test ([#354](https://github.com/iamkun/dayjs/issues/354)) ([6fca6d5](https://github.com/iamkun/dayjs/commit/6fca6d5))

## [1.7.6](https://github.com/iamkun/dayjs/compare/v1.7.5...v1.7.6) (2018-09-25)


### Bug Fixes

* **add dayjs.unix:** add dayjs.unix to parse timestamp in seconds && locale update ([5711c5e](https://github.com/iamkun/dayjs/commit/5711c5e))

## [1.7.5](https://github.com/iamkun/dayjs/compare/v1.7.4...v1.7.5) (2018-08-10)


### Bug Fixes

* add isBetween API & update ([b5fc3d1](https://github.com/iamkun/dayjs/commit/b5fc3d1))

## [1.7.4](https://github.com/iamkun/dayjs/compare/v1.7.3...v1.7.4) (2018-07-11)


### Bug Fixes

* update set week logic ([60b6325](https://github.com/iamkun/dayjs/commit/60b6325)), closes [#276](https://github.com/iamkun/dayjs/issues/276)

## [1.7.3](https://github.com/iamkun/dayjs/compare/v1.7.2...v1.7.3) (2018-07-10)


### Bug Fixes

* **locale-nl:** set correct weekdays and months ([6d089d7](https://github.com/iamkun/dayjs/commit/6d089d7))

## [1.7.2](https://github.com/iamkun/dayjs/compare/v1.7.1...v1.7.2) (2018-07-04)


### Bug Fixes

* DEPRECATED isLeapYear, use IsLeapYear plugin instead ([e2e5116](https://github.com/iamkun/dayjs/commit/e2e5116))

## [1.7.1](https://github.com/iamkun/dayjs/compare/v1.7.0...v1.7.1) (2018-07-03)


### Bug Fixes

* fix week() error near the end of the year ([fa03689](https://github.com/iamkun/dayjs/commit/fa03689))

# [1.7.0](https://github.com/iamkun/dayjs/compare/v1.6.10...v1.7.0) (2018-07-02)


### Features

* Added method `.week()` to retrieve week of the year ([e1c1b1c](https://github.com/iamkun/dayjs/commit/e1c1b1c))
* Updated Japanese locae

## [1.6.10](https://github.com/iamkun/dayjs/compare/v1.6.9...v1.6.10) (2018-06-25)


### Bug Fixes

* Add relative locales to russian language ([c7e9898](https://github.com/iamkun/dayjs/commit/c7e9898)), closes [#256](https://github.com/iamkun/dayjs/issues/256)

## [1.6.9](https://github.com/iamkun/dayjs/compare/v1.6.8...v1.6.9) (2018-06-14)


### Bug Fixes

* add isDayjs => boolean API ([6227c8b](https://github.com/iamkun/dayjs/commit/6227c8b))

## [1.6.8](https://github.com/iamkun/dayjs/compare/v1.6.7...v1.6.8) (2018-06-14)


### Bug Fixes

* fix  Advanced format bug in zh-cn ([0c07874](https://github.com/iamkun/dayjs/commit/0c07874)), closes [#242](https://github.com/iamkun/dayjs/issues/242)

## [1.6.7](https://github.com/iamkun/dayjs/compare/v1.6.6...v1.6.7) (2018-06-11)


### Bug Fixes

* fix id locale ([1ebbeb8](https://github.com/iamkun/dayjs/commit/1ebbeb8)), closes [#234](https://github.com/iamkun/dayjs/issues/234)

<a name="1.6.6"></a>
## [1.6.6](https://github.com/iamkun/dayjs/compare/v1.6.5...v1.6.6) (2018-06-06)


### Bug Fixes

*  format API update and locale file update ([5ca48f0](https://github.com/iamkun/dayjs/commit/5ca48f0)), closes [#228](https://github.com/iamkun/dayjs/issues/228)

<a name="1.6.5"></a>
## [1.6.5](https://github.com/iamkun/dayjs/compare/v1.6.4...v1.6.5) (2018-05-31)


### Bug Fixes

* bugfix, utils update and  locale file update ([ebcb6d5](https://github.com/iamkun/dayjs/commit/ebcb6d5)), closes [#214](https://github.com/iamkun/dayjs/issues/214)

<a name="1.6.4"></a>
## [1.6.4](https://github.com/iamkun/dayjs/compare/v1.6.3...v1.6.4) (2018-05-25)


### Bug Fixes

* add RelativeTime plugin and locale file update ([c1fbbca](https://github.com/iamkun/dayjs/commit/c1fbbca)), closes [#198](https://github.com/iamkun/dayjs/issues/198)

<a name="1.6.3"></a>
## [1.6.3](https://github.com/iamkun/dayjs/compare/v1.6.2...v1.6.3) (2018-05-21)


### Bug Fixes

* Changing locales locally is immutable from this release ([2cce729](https://github.com/iamkun/dayjs/commit/2cce729)), closes [#182](https://github.com/iamkun/dayjs/issues/182)
* instance locale change should be immutable ([84597c9](https://github.com/iamkun/dayjs/commit/84597c9))
* Add more locales
* english ordinal fix

<a name="1.6.2"></a>
## [1.6.2](https://github.com/iamkun/dayjs/compare/v1.6.1...v1.6.2) (2018-05-18)


### Bug Fixes

* change-log update && test new npm release ([aa49cba](https://github.com/iamkun/dayjs/commit/aa49cba)), closes [#163](https://github.com/iamkun/dayjs/issues/163)

<a name="1.6.1"></a>
## [1.6.1](https://github.com/iamkun/dayjs/compare/v1.6.0...v1.6.1) (2018-05-18)


### Bug Fixes

* Add German, Brazilian Portuguese locales
* add() & parse() bug fix & add locale de, pt-br ([bf1331e](https://github.com/iamkun/dayjs/commit/bf1331e))

<a name="1.6.0"></a>
# [1.6.0](https://github.com/iamkun/dayjs/compare/v1.5.24...v1.6.0) (2018-05-15)


### Features

* Locale && Plugin ([2342c55](https://github.com/iamkun/dayjs/commit/2342c55)), closes [#141](https://github.com/iamkun/dayjs/issues/141)

<?php

namespace ContainerJC7y5Zo;

return [
    '.1_Serializer~XO.cFXO' => true,
    '.1_ServiceLocator~vQ4f5W7' => true,
    '.2_ObjectNormalizer~XO.cFXO' => true,
    '.App\\Command\\AssignCoursesToApprenantCommand.lazy' => true,
    '.App\\Command\\CreateDefaultAdminCommand.lazy' => true,
    '.App\\Command\\FixQuizTypeCommand.lazy' => true,
    '.App\\Command\\MigrateQuizDataCommand.lazy' => true,
    '.App\\Command\\MigrateRelationsCommand.lazy' => true,
    '.App\\Command\\SynchronizeIdmoduleCommand.lazy' => true,
    '.App\\Command\\TestEmailCommand.lazy' => true,
    '.App\\Command\\UpdateEvaluationIdmoduleCommand.lazy' => true,
    '.App\\Command\\WebSocketServerCommand.lazy' => true,
    '.Psr\\Container\\ContainerInterface $parameter_bag' => true,
    '.Psr\\Log\\LoggerInterface $asset_mapperLogger' => true,
    '.Psr\\Log\\LoggerInterface $http_clientLogger' => true,
    '.abstract.instanceof.App\\Command\\AssignCoursesToApprenantCommand' => true,
    '.abstract.instanceof.App\\Command\\CreateDefaultAdminCommand' => true,
    '.abstract.instanceof.App\\Command\\FixQuizTypeCommand' => true,
    '.abstract.instanceof.App\\Command\\MigrateQuizDataCommand' => true,
    '.abstract.instanceof.App\\Command\\MigrateRelationsCommand' => true,
    '.abstract.instanceof.App\\Command\\SynchronizeIdmoduleCommand' => true,
    '.abstract.instanceof.App\\Command\\TestEmailCommand' => true,
    '.abstract.instanceof.App\\Command\\UpdateEvaluationIdmoduleCommand' => true,
    '.abstract.instanceof.App\\Command\\WebSocketServerCommand' => true,
    '.abstract.instanceof.App\\Controller\\ActionController' => true,
    '.abstract.instanceof.App\\Controller\\AdminController' => true,
    '.abstract.instanceof.App\\Controller\\ApprenantController' => true,
    '.abstract.instanceof.App\\Controller\\AuthController' => true,
    '.abstract.instanceof.App\\Controller\\CertificatController' => true,
    '.abstract.instanceof.App\\Controller\\CompetenceController' => true,
    '.abstract.instanceof.App\\Controller\\CourseController' => true,
    '.abstract.instanceof.App\\Controller\\DashboardController' => true,
    '.abstract.instanceof.App\\Controller\\DiagnosticController' => true,
    '.abstract.instanceof.App\\Controller\\EvaluationController' => true,
    '.abstract.instanceof.App\\Controller\\EvaluationDetailController' => true,
    '.abstract.instanceof.App\\Controller\\EvenementController' => true,
    '.abstract.instanceof.App\\Controller\\FileUploadController' => true,
    '.abstract.instanceof.App\\Controller\\FixController' => true,
    '.abstract.instanceof.App\\Controller\\FormateurController' => true,
    '.abstract.instanceof.App\\Controller\\MessagerieController' => true,
    '.abstract.instanceof.App\\Controller\\NotificationController' => true,
    '.abstract.instanceof.App\\Controller\\ProgressionController' => true,
    '.abstract.instanceof.App\\Controller\\QuizController' => true,
    '.abstract.instanceof.App\\Controller\\ReclamationController' => true,
    '.abstract.instanceof.App\\Controller\\SousCompetenceController' => true,
    '.abstract.instanceof.App\\Repository\\ActionRepository' => true,
    '.abstract.instanceof.App\\Repository\\AdministrateurRepository' => true,
    '.abstract.instanceof.App\\Repository\\ApprenantRepository' => true,
    '.abstract.instanceof.App\\Repository\\CertificatRepository' => true,
    '.abstract.instanceof.App\\Repository\\CompetenceRepository' => true,
    '.abstract.instanceof.App\\Repository\\CoursRepository' => true,
    '.abstract.instanceof.App\\Repository\\EvaluationDetailRepository' => true,
    '.abstract.instanceof.App\\Repository\\EvaluationRepository' => true,
    '.abstract.instanceof.App\\Repository\\EvenementRepository' => true,
    '.abstract.instanceof.App\\Repository\\FormateurRepository' => true,
    '.abstract.instanceof.App\\Repository\\MessagerieRepository' => true,
    '.abstract.instanceof.App\\Repository\\NotificationRepository' => true,
    '.abstract.instanceof.App\\Repository\\ProgressionRepository' => true,
    '.abstract.instanceof.App\\Repository\\QuizRepository' => true,
    '.abstract.instanceof.App\\Repository\\ReclamationRepository' => true,
    '.abstract.instanceof.App\\Repository\\SousCompetenceRepository' => true,
    '.abstract.instanceof.App\\Repository\\UtilisateurRepository' => true,
    '.asset_mapper.command.compile.lazy' => true,
    '.asset_mapper.command.debug.lazy' => true,
    '.asset_mapper.importmap.command.audit.lazy' => true,
    '.asset_mapper.importmap.command.install.lazy' => true,
    '.asset_mapper.importmap.command.outdated.lazy' => true,
    '.asset_mapper.importmap.command.remove.lazy' => true,
    '.asset_mapper.importmap.command.require.lazy' => true,
    '.asset_mapper.importmap.command.update.lazy' => true,
    '.cache_connection.GD_MSZC' => true,
    '.cache_connection.JKE6keX' => true,
    '.console.command.about.lazy' => true,
    '.console.command.assets_install.lazy' => true,
    '.console.command.cache_clear.lazy' => true,
    '.console.command.cache_pool_clear.lazy' => true,
    '.console.command.cache_pool_delete.lazy' => true,
    '.console.command.cache_pool_invalidate_tags.lazy' => true,
    '.console.command.cache_pool_list.lazy' => true,
    '.console.command.cache_pool_prune.lazy' => true,
    '.console.command.cache_warmup.lazy' => true,
    '.console.command.config_debug.lazy' => true,
    '.console.command.config_dump_reference.lazy' => true,
    '.console.command.container_debug.lazy' => true,
    '.console.command.container_lint.lazy' => true,
    '.console.command.debug_autowiring.lazy' => true,
    '.console.command.dotenv_debug.lazy' => true,
    '.console.command.event_dispatcher_debug.lazy' => true,
    '.console.command.form_debug.lazy' => true,
    '.console.command.mailer_test.lazy' => true,
    '.console.command.messenger_consume_messages.lazy' => true,
    '.console.command.messenger_debug.lazy' => true,
    '.console.command.messenger_failed_messages_remove.lazy' => true,
    '.console.command.messenger_failed_messages_retry.lazy' => true,
    '.console.command.messenger_failed_messages_show.lazy' => true,
    '.console.command.messenger_setup_transports.lazy' => true,
    '.console.command.messenger_stats.lazy' => true,
    '.console.command.messenger_stop_workers.lazy' => true,
    '.console.command.router_debug.lazy' => true,
    '.console.command.router_match.lazy' => true,
    '.console.command.secrets_decrypt_to_local.lazy' => true,
    '.console.command.secrets_encrypt_from_local.lazy' => true,
    '.console.command.secrets_generate_key.lazy' => true,
    '.console.command.secrets_list.lazy' => true,
    '.console.command.secrets_remove.lazy' => true,
    '.console.command.secrets_set.lazy' => true,
    '.console.command.serializer_debug.lazy' => true,
    '.console.command.translation_debug.lazy' => true,
    '.console.command.translation_extract.lazy' => true,
    '.console.command.translation_pull.lazy' => true,
    '.console.command.translation_push.lazy' => true,
    '.console.command.validator_debug.lazy' => true,
    '.console.command.xliff_lint.lazy' => true,
    '.console.command.yaml_lint.lazy' => true,
    '.data_collector.command' => true,
    '.debug.http_client' => true,
    '.debug.http_client.inner' => true,
    '.debug.security.voter.security.access.authenticated_voter' => true,
    '.debug.security.voter.security.access.expression_voter' => true,
    '.debug.security.voter.security.access.simple_role_voter' => true,
    '.debug.serializer.encoder.api_platform.jsonld.encoder' => true,
    '.debug.serializer.encoder.api_platform.jsonopenapi.encoder' => true,
    '.debug.serializer.encoder.api_platform.problem.encoder' => true,
    '.debug.serializer.encoder.api_platform.yamlopenapi.encoder' => true,
    '.debug.serializer.encoder.serializer.encoder.csv' => true,
    '.debug.serializer.encoder.serializer.encoder.json' => true,
    '.debug.serializer.encoder.serializer.encoder.xml' => true,
    '.debug.serializer.encoder.serializer.encoder.yaml' => true,
    '.debug.serializer.normalizer.api_platform.hydra.normalizer.collection' => true,
    '.debug.serializer.normalizer.api_platform.hydra.normalizer.constraint_violation_list' => true,
    '.debug.serializer.normalizer.api_platform.hydra.normalizer.documentation' => true,
    '.debug.serializer.normalizer.api_platform.hydra.normalizer.entrypoint' => true,
    '.debug.serializer.normalizer.api_platform.hydra.normalizer.error' => true,
    '.debug.serializer.normalizer.api_platform.jsonld.normalizer.error' => true,
    '.debug.serializer.normalizer.api_platform.jsonld.normalizer.item' => true,
    '.debug.serializer.normalizer.api_platform.jsonld.normalizer.object' => true,
    '.debug.serializer.normalizer.api_platform.jsonld.normalizer.validation_exception' => true,
    '.debug.serializer.normalizer.api_platform.normalizer.constraint_violation_list' => true,
    '.debug.serializer.normalizer.api_platform.openapi.normalizer' => true,
    '.debug.serializer.normalizer.api_platform.openapi.normalizer.api_gateway' => true,
    '.debug.serializer.normalizer.api_platform.openapi.normalizer.legacy' => true,
    '.debug.serializer.normalizer.api_platform.problem.normalizer.constraint_violation_list' => true,
    '.debug.serializer.normalizer.api_platform.problem.normalizer.error' => true,
    '.debug.serializer.normalizer.api_platform.problem.normalizer.validation_exception' => true,
    '.debug.serializer.normalizer.api_platform.serializer.normalizer.item' => true,
    '.debug.serializer.normalizer.serializer.denormalizer.array' => true,
    '.debug.serializer.normalizer.serializer.denormalizer.unwrapping' => true,
    '.debug.serializer.normalizer.serializer.normalizer.backed_enum' => true,
    '.debug.serializer.normalizer.serializer.normalizer.constraint_violation_list' => true,
    '.debug.serializer.normalizer.serializer.normalizer.data_uri' => true,
    '.debug.serializer.normalizer.serializer.normalizer.dateinterval' => true,
    '.debug.serializer.normalizer.serializer.normalizer.datetime' => true,
    '.debug.serializer.normalizer.serializer.normalizer.datetimezone' => true,
    '.debug.serializer.normalizer.serializer.normalizer.flatten_exception' => true,
    '.debug.serializer.normalizer.serializer.normalizer.form_error' => true,
    '.debug.serializer.normalizer.serializer.normalizer.json_serializable' => true,
    '.debug.serializer.normalizer.serializer.normalizer.mime_message' => true,
    '.debug.serializer.normalizer.serializer.normalizer.object' => true,
    '.debug.serializer.normalizer.serializer.normalizer.problem' => true,
    '.debug.serializer.normalizer.serializer.normalizer.translatable' => true,
    '.debug.serializer.normalizer.serializer.normalizer.uid' => true,
    '.debug.value_resolver.api_platform.argument_resolver.payload' => true,
    '.debug.value_resolver.argument_resolver.backed_enum_resolver' => true,
    '.debug.value_resolver.argument_resolver.datetime' => true,
    '.debug.value_resolver.argument_resolver.default' => true,
    '.debug.value_resolver.argument_resolver.not_tagged_controller' => true,
    '.debug.value_resolver.argument_resolver.query_parameter_value_resolver' => true,
    '.debug.value_resolver.argument_resolver.request' => true,
    '.debug.value_resolver.argument_resolver.request_attribute' => true,
    '.debug.value_resolver.argument_resolver.request_payload' => true,
    '.debug.value_resolver.argument_resolver.service' => true,
    '.debug.value_resolver.argument_resolver.session' => true,
    '.debug.value_resolver.argument_resolver.variadic' => true,
    '.debug.value_resolver.doctrine.orm.entity_value_resolver' => true,
    '.debug.value_resolver.security.security_token_value_resolver' => true,
    '.debug.value_resolver.security.user_value_resolver' => true,
    '.doctrine.orm.default_metadata_driver' => true,
    '.doctrine.orm.default_metadata_driver.inner' => true,
    '.doctrine_migrations.current_command.lazy' => true,
    '.doctrine_migrations.diff_command.lazy' => true,
    '.doctrine_migrations.dump_schema_command.lazy' => true,
    '.doctrine_migrations.execute_command.lazy' => true,
    '.doctrine_migrations.generate_command.lazy' => true,
    '.doctrine_migrations.latest_command.lazy' => true,
    '.doctrine_migrations.migrate_command.lazy' => true,
    '.doctrine_migrations.rollup_command.lazy' => true,
    '.doctrine_migrations.status_command.lazy' => true,
    '.doctrine_migrations.sync_metadata_command.lazy' => true,
    '.doctrine_migrations.up_to_date_command.lazy' => true,
    '.doctrine_migrations.version_command.lazy' => true,
    '.doctrine_migrations.versions_command.lazy' => true,
    '.errored..service_locator.y4_Zrx..Symfony\\Component\\Config\\Loader\\LoaderInterface' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\ActionRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\AdministrateurRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\ApprenantRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\CertificatRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\CompetenceRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\CoursRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\EvaluationDetailRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\EvaluationRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\EvenementRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\FormateurRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\MessagerieRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\NotificationRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\ProgressionRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\QuizRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\ReclamationRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\SousCompetenceRepository' => true,
    '.instanceof.Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepositoryInterface.0.App\\Repository\\UtilisateurRepository' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\ActionController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\AdminController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\ApprenantController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\AuthController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\CertificatController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\CompetenceController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\CourseController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\DashboardController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\DiagnosticController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\EvaluationController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\EvaluationDetailController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\EvenementController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\FileUploadController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\FixController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\FormateurController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\MessagerieController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\NotificationController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\ProgressionController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\QuizController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\ReclamationController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.App\\Controller\\SousCompetenceController' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\AssignCoursesToApprenantCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\CreateDefaultAdminCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\FixQuizTypeCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\MigrateQuizDataCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\MigrateRelationsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\SynchronizeIdmoduleCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\TestEmailCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\UpdateEvaluationIdmoduleCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.App\\Command\\WebSocketServerCommand' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\ActionController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\AdminController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\ApprenantController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\AuthController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\CertificatController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\CompetenceController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\CourseController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\DashboardController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\DiagnosticController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\EvaluationController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\EvaluationDetailController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\EvenementController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\FileUploadController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\FixController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\FormateurController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\MessagerieController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\NotificationController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\ProgressionController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\QuizController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\ReclamationController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.App\\Controller\\SousCompetenceController' => true,
    '.lazy_profiler' => true,
    '.maker.auto_command.make_auth.lazy' => true,
    '.maker.auto_command.make_command.lazy' => true,
    '.maker.auto_command.make_controller.lazy' => true,
    '.maker.auto_command.make_crud.lazy' => true,
    '.maker.auto_command.make_docker_database.lazy' => true,
    '.maker.auto_command.make_entity.lazy' => true,
    '.maker.auto_command.make_fixtures.lazy' => true,
    '.maker.auto_command.make_form.lazy' => true,
    '.maker.auto_command.make_listener.lazy' => true,
    '.maker.auto_command.make_message.lazy' => true,
    '.maker.auto_command.make_messenger_middleware.lazy' => true,
    '.maker.auto_command.make_migration.lazy' => true,
    '.maker.auto_command.make_registration_form.lazy' => true,
    '.maker.auto_command.make_reset_password.lazy' => true,
    '.maker.auto_command.make_schedule.lazy' => true,
    '.maker.auto_command.make_security_custom.lazy' => true,
    '.maker.auto_command.make_security_form_login.lazy' => true,
    '.maker.auto_command.make_serializer_encoder.lazy' => true,
    '.maker.auto_command.make_serializer_normalizer.lazy' => true,
    '.maker.auto_command.make_state_processor.lazy' => true,
    '.maker.auto_command.make_state_provider.lazy' => true,
    '.maker.auto_command.make_stimulus_controller.lazy' => true,
    '.maker.auto_command.make_test.lazy' => true,
    '.maker.auto_command.make_twig_component.lazy' => true,
    '.maker.auto_command.make_twig_extension.lazy' => true,
    '.maker.auto_command.make_user.lazy' => true,
    '.maker.auto_command.make_validator.lazy' => true,
    '.maker.auto_command.make_voter.lazy' => true,
    '.maker.auto_command.make_webhook.lazy' => true,
    '.messenger.handler_descriptor.6kVvRT.' => true,
    '.messenger.handler_descriptor.Lml2ICs' => true,
    '.messenger.handler_descriptor.QXXNQ9d' => true,
    '.messenger.handler_descriptor.XZowc.T' => true,
    '.messenger.handler_descriptor.kEzMhfs' => true,
    '.messenger.handler_descriptor.p4Qvabm' => true,
    '.messenger.handler_descriptor.tGvt0LH' => true,
    '.messenger.handler_descriptor.vMw0m61' => true,
    '.monolog.command.server_log.lazy' => true,
    '.security.command.debug_firewall.lazy' => true,
    '.security.command.user_password_hash.lazy' => true,
    '.security.request_matcher.0QxrXJt' => true,
    '.security.request_matcher.10Ydh3R' => true,
    '.security.request_matcher.6M.XeUm' => true,
    '.security.request_matcher.9KckBRV' => true,
    '.security.request_matcher.AMZT15Y' => true,
    '.security.request_matcher.Bs7fT.P' => true,
    '.security.request_matcher.EHwIQxq' => true,
    '.security.request_matcher.EWvmMUH' => true,
    '.security.request_matcher.GAenj0B' => true,
    '.security.request_matcher.I7_M384' => true,
    '.security.request_matcher.L9Rm4Ps' => true,
    '.security.request_matcher.LfGhkZS' => true,
    '.security.request_matcher.PmtPXX5' => true,
    '.security.request_matcher.U0hTl7f' => true,
    '.security.request_matcher.bMkCtbi' => true,
    '.security.request_matcher.bnSwo2p' => true,
    '.security.request_matcher.gOxUUeP' => true,
    '.security.request_matcher.gjnNpJn' => true,
    '.security.request_matcher.h2Ry86l' => true,
    '.security.request_matcher.iRmxrxk' => true,
    '.security.request_matcher.j6QiL_4' => true,
    '.security.request_matcher.kLbKLHa' => true,
    '.security.request_matcher.lYoNu.L' => true,
    '.security.request_matcher.lyVOED.' => true,
    '.security.request_matcher.ojmWZ8s' => true,
    '.security.request_matcher.q1UFWmc' => true,
    '.security.request_matcher.tUbx_nW' => true,
    '.security.request_matcher.vgIvNvV' => true,
    '.security.request_matcher.vhy2oy3' => true,
    '.service_locator..2cOjlF' => true,
    '.service_locator..Fs8Kd7' => true,
    '.service_locator.0TACwl3' => true,
    '.service_locator.5cAhUFF' => true,
    '.service_locator.6YvL4e3' => true,
    '.service_locator.80edgLI' => true,
    '.service_locator.BFrsqsn' => true,
    '.service_locator.B_yLtTQ' => true,
    '.service_locator.CNIVqaq' => true,
    '.service_locator.CQiFQkx' => true,
    '.service_locator.Cj4aUBQ' => true,
    '.service_locator.DNIKdb2' => true,
    '.service_locator.F9PKc.7' => true,
    '.service_locator.FaCy.MC' => true,
    '.service_locator.KFPsYtg' => true,
    '.service_locator.KLVvNIq' => true,
    '.service_locator.Kl0E_Fe' => true,
    '.service_locator.LD5oJC8' => true,
    '.service_locator.LcVn9Hr' => true,
    '.service_locator.Lo8jEUu' => true,
    '.service_locator.MFVMisY' => true,
    '.service_locator.MY0SdHE' => true,
    '.service_locator.MfxSDRP' => true,
    '.service_locator.Mhqdd2r' => true,
    '.service_locator.NBUFN6A' => true,
    '.service_locator.O24_MAy' => true,
    '.service_locator.O2p6Lk7' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\ActionController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\AdminController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\ApprenantController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\AuthController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\CertificatController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\CompetenceController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\CourseController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\DashboardController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\DiagnosticController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\EvaluationController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\EvaluationDetailController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\EvenementController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\FileUploadController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\FixController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\FormateurController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\MessagerieController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\NotificationController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\ProgressionController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\QuizController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\ReclamationController' => true,
    '.service_locator.O2p6Lk7.App\\Controller\\SousCompetenceController' => true,
    '.service_locator.Oannbdp' => true,
    '.service_locator.OrpkBWh' => true,
    '.service_locator.PQOdg4m' => true,
    '.service_locator.Q1z.f9O' => true,
    '.service_locator.QxhUvdh' => true,
    '.service_locator.SsDhI2w' => true,
    '.service_locator.TpoC7U9' => true,
    '.service_locator.UKaMcTq' => true,
    '.service_locator.VDh5GQW' => true,
    '.service_locator.W47vLMj' => true,
    '.service_locator.XXv1IfR' => true,
    '.service_locator.Xbsa8iG' => true,
    '.service_locator.XtmiKbi' => true,
    '.service_locator.Y4J.A.e' => true,
    '.service_locator.Za9MpXe' => true,
    '.service_locator._kIAbz1' => true,
    '.service_locator._wyX1Dp' => true,
    '.service_locator.bJ.4HC5' => true,
    '.service_locator.bN.V_Nq' => true,
    '.service_locator.bN.V_Nq.router.default' => true,
    '.service_locator.bnaLdkn' => true,
    '.service_locator.c7f47p7' => true,
    '.service_locator.cUcW89y' => true,
    '.service_locator.cUcW89y.router.cache_warmer' => true,
    '.service_locator.cxvgam4' => true,
    '.service_locator.df1HHDL' => true,
    '.service_locator.e_.xxAP' => true,
    '.service_locator.ehYDEiZ' => true,
    '.service_locator.etVElvN' => true,
    '.service_locator.etVElvN.twig.template_cache_warmer' => true,
    '.service_locator.f0CMdBy' => true,
    '.service_locator.fuYM_Z.' => true,
    '.service_locator.fuYM_Z..translation.warmer' => true,
    '.service_locator.gFlme_s' => true,
    '.service_locator.gvCNacY' => true,
    '.service_locator.gwlZsVd' => true,
    '.service_locator.h8Jkkd2' => true,
    '.service_locator.hnz5ZNh' => true,
    '.service_locator.i0..Ry2' => true,
    '.service_locator.j6IoHy0' => true,
    '.service_locator.j8PmV4j' => true,
    '.service_locator.jUv.zyj' => true,
    '.service_locator.kkPb0Tz' => true,
    '.service_locator.lLv4pWF' => true,
    '.service_locator.mOufrFG' => true,
    '.service_locator.mosr3RH' => true,
    '.service_locator.qssr6JI' => true,
    '.service_locator.ro9MXaF' => true,
    '.service_locator.sL05zc0' => true,
    '.service_locator.th9eKxf' => true,
    '.service_locator.uNJrUh1' => true,
    '.service_locator.um7O_4Y' => true,
    '.service_locator.v5NSJjB' => true,
    '.service_locator.w7.f4fT' => true,
    '.service_locator.x97CcwG' => true,
    '.service_locator.y4_Zrx.' => true,
    '.service_locator.y4dplYt' => true,
    '.service_locator.yJ1DcjE' => true,
    '.service_locator.yY.p4P7' => true,
    '.service_locator.zCMhoWZ' => true,
    '.service_locator.zcZ.KbZ' => true,
    '.twig.command.debug.lazy' => true,
    '.twig.command.lint.lazy' => true,
    '.var_dumper.command.server_dump.lazy' => true,
    'ApiPlatform\\Api\\IdentifiersExtractorInterface' => true,
    'ApiPlatform\\Api\\IriConverterInterface' => true,
    'ApiPlatform\\Api\\ResourceClassResolverInterface' => true,
    'ApiPlatform\\Api\\UrlGeneratorInterface' => true,
    'ApiPlatform\\Doctrine\\Common\\State\\PersistProcessor' => true,
    'ApiPlatform\\Doctrine\\Common\\State\\RemoveProcessor' => true,
    'ApiPlatform\\Doctrine\\Orm\\Extension\\EagerLoadingExtension' => true,
    'ApiPlatform\\Doctrine\\Orm\\Extension\\FilterEagerLoadingExtension' => true,
    'ApiPlatform\\Doctrine\\Orm\\Extension\\FilterExtension' => true,
    'ApiPlatform\\Doctrine\\Orm\\Extension\\OrderExtension' => true,
    'ApiPlatform\\Doctrine\\Orm\\Extension\\PaginationExtension' => true,
    'ApiPlatform\\Doctrine\\Orm\\Extension\\ParameterExtension' => true,
    'ApiPlatform\\Doctrine\\Orm\\Filter\\BackedEnumFilter' => true,
    'ApiPlatform\\Doctrine\\Orm\\Filter\\BooleanFilter' => true,
    'ApiPlatform\\Doctrine\\Orm\\Filter\\DateFilter' => true,
    'ApiPlatform\\Doctrine\\Orm\\Filter\\ExistsFilter' => true,
    'ApiPlatform\\Doctrine\\Orm\\Filter\\NumericFilter' => true,
    'ApiPlatform\\Doctrine\\Orm\\Filter\\OrderFilter' => true,
    'ApiPlatform\\Doctrine\\Orm\\Filter\\RangeFilter' => true,
    'ApiPlatform\\Doctrine\\Orm\\Filter\\SearchFilter' => true,
    'ApiPlatform\\Doctrine\\Orm\\State\\CollectionProvider' => true,
    'ApiPlatform\\Doctrine\\Orm\\State\\ItemProvider' => true,
    'ApiPlatform\\JsonSchema\\SchemaFactoryInterface' => true,
    'ApiPlatform\\JsonSchema\\TypeFactoryInterface' => true,
    'ApiPlatform\\Metadata\\IdentifiersExtractorInterface' => true,
    'ApiPlatform\\Metadata\\IriConverterInterface' => true,
    'ApiPlatform\\Metadata\\Operation\\Factory\\OperationMetadataFactoryInterface' => true,
    'ApiPlatform\\Metadata\\Property\\Factory\\PropertyMetadataFactoryInterface' => true,
    'ApiPlatform\\Metadata\\Property\\Factory\\PropertyNameCollectionFactoryInterface' => true,
    'ApiPlatform\\Metadata\\ResourceClassResolverInterface' => true,
    'ApiPlatform\\Metadata\\Resource\\Factory\\ResourceMetadataCollectionFactoryInterface' => true,
    'ApiPlatform\\Metadata\\Resource\\Factory\\ResourceNameCollectionFactoryInterface' => true,
    'ApiPlatform\\Metadata\\UrlGeneratorInterface' => true,
    'ApiPlatform\\OpenApi\\Factory\\OpenApiFactoryInterface' => true,
    'ApiPlatform\\OpenApi\\Options' => true,
    'ApiPlatform\\OpenApi\\Serializer\\OpenApiNormalizer' => true,
    'ApiPlatform\\Serializer\\Filter\\GroupFilter' => true,
    'ApiPlatform\\Serializer\\Filter\\PropertyFilter' => true,
    'ApiPlatform\\Serializer\\SerializerContextBuilderInterface' => true,
    'ApiPlatform\\State\\CreateProvider' => true,
    'ApiPlatform\\State\\ObjectProvider' => true,
    'ApiPlatform\\State\\Pagination\\Pagination' => true,
    'ApiPlatform\\State\\Pagination\\PaginationOptions' => true,
    'ApiPlatform\\State\\SerializerContextBuilderInterface' => true,
    'ApiPlatform\\Symfony\\Messenger\\Processor' => true,
    'ApiPlatform\\Symfony\\Security\\ResourceAccessCheckerInterface' => true,
    'ApiPlatform\\Validator\\ValidatorInterface' => true,
    'App\\Command\\AssignCoursesToApprenantCommand' => true,
    'App\\Command\\CreateDefaultAdminCommand' => true,
    'App\\Command\\FixQuizTypeCommand' => true,
    'App\\Command\\MigrateQuizDataCommand' => true,
    'App\\Command\\MigrateRelationsCommand' => true,
    'App\\Command\\SynchronizeIdmoduleCommand' => true,
    'App\\Command\\TestEmailCommand' => true,
    'App\\Command\\UpdateEvaluationIdmoduleCommand' => true,
    'App\\Command\\WebSocketServerCommand' => true,
    'App\\Entity' => true,
    'App\\Migrations\\Version20250501000000' => true,
    'App\\Repository\\ActionRepository' => true,
    'App\\Repository\\AdministrateurRepository' => true,
    'App\\Repository\\ApprenantRepository' => true,
    'App\\Repository\\CertificatRepository' => true,
    'App\\Repository\\CompetenceRepository' => true,
    'App\\Repository\\CoursRepository' => true,
    'App\\Repository\\EvaluationDetailRepository' => true,
    'App\\Repository\\EvaluationRepository' => true,
    'App\\Repository\\EvenementRepository' => true,
    'App\\Repository\\FormateurRepository' => true,
    'App\\Repository\\MessagerieRepository' => true,
    'App\\Repository\\NotificationRepository' => true,
    'App\\Repository\\ProgressionRepository' => true,
    'App\\Repository\\QuizRepository' => true,
    'App\\Repository\\ReclamationRepository' => true,
    'App\\Repository\\SousCompetenceRepository' => true,
    'App\\Repository\\UtilisateurRepository' => true,
    'App\\Security\\JwtAuthenticator' => true,
    'App\\Service\\EmailService' => true,
    'App\\Service\\WebSocketNotificationService' => true,
    'App\\WebSocket\\NotificationServer' => true,
    'Doctrine\\Bundle\\DoctrineBundle\\Dbal\\ManagerRegistryAwareConnectionProvider' => true,
    'Doctrine\\Common\\Persistence\\ManagerRegistry' => true,
    'Doctrine\\DBAL\\Connection' => true,
    'Doctrine\\DBAL\\Connection $defaultConnection' => true,
    'Doctrine\\DBAL\\Tools\\Console\\Command\\RunSqlCommand' => true,
    'Doctrine\\ORM\\EntityManagerInterface' => true,
    'Doctrine\\ORM\\EntityManagerInterface $defaultEntityManager' => true,
    'Doctrine\\Persistence\\ManagerRegistry' => true,
    'Psr\\Cache\\CacheItemPoolInterface' => true,
    'Psr\\Clock\\ClockInterface' => true,
    'Psr\\Container\\ContainerInterface $parameterBag' => true,
    'Psr\\EventDispatcher\\EventDispatcherInterface' => true,
    'Psr\\Log\\LoggerInterface' => true,
    'Psr\\Log\\LoggerInterface $assetMapperLogger' => true,
    'Psr\\Log\\LoggerInterface $cacheLogger' => true,
    'Psr\\Log\\LoggerInterface $chatbotLogger' => true,
    'Psr\\Log\\LoggerInterface $consoleLogger' => true,
    'Psr\\Log\\LoggerInterface $debugLogger' => true,
    'Psr\\Log\\LoggerInterface $deprecationLogger' => true,
    'Psr\\Log\\LoggerInterface $doctrineLogger' => true,
    'Psr\\Log\\LoggerInterface $eventLogger' => true,
    'Psr\\Log\\LoggerInterface $httpClientLogger' => true,
    'Psr\\Log\\LoggerInterface $mailerLogger' => true,
    'Psr\\Log\\LoggerInterface $messengerLogger' => true,
    'Psr\\Log\\LoggerInterface $phpLogger' => true,
    'Psr\\Log\\LoggerInterface $profilerLogger' => true,
    'Psr\\Log\\LoggerInterface $requestLogger' => true,
    'Psr\\Log\\LoggerInterface $routerLogger' => true,
    'Psr\\Log\\LoggerInterface $securityLogger' => true,
    'Psr\\Log\\LoggerInterface $translationLogger' => true,
    'SessionHandlerInterface' => true,
    'Symfony\\Bundle\\SecurityBundle\\Security' => true,
    'Symfony\\Component\\AssetMapper\\AssetMapperInterface' => true,
    'Symfony\\Component\\AssetMapper\\ImportMap\\ImportMapManager' => true,
    'Symfony\\Component\\Asset\\Packages' => true,
    'Symfony\\Component\\Clock\\ClockInterface' => true,
    'Symfony\\Component\\Config\\Loader\\LoaderInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ContainerBagInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ParameterBagInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ReverseContainer' => true,
    'Symfony\\Component\\ErrorHandler\\ErrorRenderer\\FileLinkFormatter' => true,
    'Symfony\\Component\\EventDispatcher\\EventDispatcherInterface' => true,
    'Symfony\\Component\\Filesystem\\Filesystem' => true,
    'Symfony\\Component\\Form\\FormFactoryInterface' => true,
    'Symfony\\Component\\Form\\FormRegistryInterface' => true,
    'Symfony\\Component\\Form\\ResolvedFormTypeFactoryInterface' => true,
    'Symfony\\Component\\HttpFoundation\\Request' => true,
    'Symfony\\Component\\HttpFoundation\\RequestStack' => true,
    'Symfony\\Component\\HttpFoundation\\Response' => true,
    'Symfony\\Component\\HttpFoundation\\Session\\SessionInterface' => true,
    'Symfony\\Component\\HttpFoundation\\UriSigner' => true,
    'Symfony\\Component\\HttpFoundation\\UrlHelper' => true,
    'Symfony\\Component\\HttpKernel\\Config\\FileLocator' => true,
    'Symfony\\Component\\HttpKernel\\Fragment\\FragmentUriGeneratorInterface' => true,
    'Symfony\\Component\\HttpKernel\\HttpCache\\StoreInterface' => true,
    'Symfony\\Component\\HttpKernel\\HttpKernelInterface' => true,
    'Symfony\\Component\\HttpKernel\\KernelInterface' => true,
    'Symfony\\Component\\HttpKernel\\UriSigner' => true,
    'Symfony\\Component\\Mailer\\MailerInterface' => true,
    'Symfony\\Component\\Mailer\\Transport\\TransportInterface' => true,
    'Symfony\\Component\\Messenger\\MessageBusInterface' => true,
    'Symfony\\Component\\Messenger\\Transport\\Serialization\\SerializerInterface' => true,
    'Symfony\\Component\\Mime\\BodyRendererInterface' => true,
    'Symfony\\Component\\Mime\\MimeTypeGuesserInterface' => true,
    'Symfony\\Component\\Mime\\MimeTypesInterface' => true,
    'Symfony\\Component\\Notifier\\NotifierInterface' => true,
    'Symfony\\Component\\PasswordHasher\\Hasher\\PasswordHasherFactoryInterface' => true,
    'Symfony\\Component\\PasswordHasher\\Hasher\\UserPasswordHasherInterface' => true,
    'Symfony\\Component\\PropertyAccess\\PropertyAccessorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyAccessExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyDescriptionExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyInfoExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyInitializableExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyListExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyReadInfoExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyTypeExtractorInterface' => true,
    'Symfony\\Component\\PropertyInfo\\PropertyWriteInfoExtractorInterface' => true,
    'Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface' => true,
    'Symfony\\Component\\Routing\\Matcher\\UrlMatcherInterface' => true,
    'Symfony\\Component\\Routing\\RequestContext' => true,
    'Symfony\\Component\\Routing\\RequestContextAwareInterface' => true,
    'Symfony\\Component\\Routing\\RouterInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authentication\\Token\\Storage\\TokenStorageInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authorization\\AccessDecisionManagerInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authorization\\AuthorizationCheckerInterface' => true,
    'Symfony\\Component\\Security\\Core\\Role\\RoleHierarchyInterface' => true,
    'Symfony\\Component\\Security\\Core\\Security' => true,
    'Symfony\\Component\\Security\\Core\\User\\UserCheckerInterface' => true,
    'Symfony\\Component\\Security\\Core\\User\\UserProviderInterface' => true,
    'Symfony\\Component\\Security\\Csrf\\CsrfTokenManagerInterface' => true,
    'Symfony\\Component\\Security\\Csrf\\TokenGenerator\\TokenGeneratorInterface' => true,
    'Symfony\\Component\\Security\\Csrf\\TokenStorage\\TokenStorageInterface' => true,
    'Symfony\\Component\\Security\\Http\\Authentication\\AuthenticationUtils' => true,
    'Symfony\\Component\\Security\\Http\\Authentication\\UserAuthenticatorInterface' => true,
    'Symfony\\Component\\Security\\Http\\Firewall' => true,
    'Symfony\\Component\\Security\\Http\\FirewallMapInterface' => true,
    'Symfony\\Component\\Security\\Http\\HttpUtils' => true,
    'Symfony\\Component\\Security\\Http\\Session\\SessionAuthenticationStrategyInterface' => true,
    'Symfony\\Component\\Serializer\\Encoder\\DecoderInterface' => true,
    'Symfony\\Component\\Serializer\\Encoder\\EncoderInterface' => true,
    'Symfony\\Component\\Serializer\\Mapping\\ClassDiscriminatorResolverInterface' => true,
    'Symfony\\Component\\Serializer\\Mapping\\Factory\\ClassMetadataFactoryInterface' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\DenormalizerInterface' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\NormalizerInterface' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\ObjectNormalizer' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\PropertyNormalizer' => true,
    'Symfony\\Component\\Serializer\\SerializerInterface' => true,
    'Symfony\\Component\\Stopwatch\\Stopwatch' => true,
    'Symfony\\Component\\String\\Slugger\\SluggerInterface' => true,
    'Symfony\\Component\\Translation\\Extractor\\ExtractorInterface' => true,
    'Symfony\\Component\\Translation\\LocaleSwitcher' => true,
    'Symfony\\Component\\Translation\\Reader\\TranslationReaderInterface' => true,
    'Symfony\\Component\\Translation\\Writer\\TranslationWriterInterface' => true,
    'Symfony\\Component\\Validator\\Constraints\\AllValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\AtLeastOneOfValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\BicValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\BlankValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CallbackValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CardSchemeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ChoiceValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CidrValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CollectionValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CompoundValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CountValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CountryValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CssColorValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\CurrencyValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\DateTimeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\DateValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\DivisibleByValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\EmailValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\EqualToValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ExpressionLanguageSyntaxValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ExpressionSyntaxValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ExpressionValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\FileValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\GreaterThanOrEqualValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\GreaterThanValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\HostnameValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IbanValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IdenticalToValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ImageValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IpValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsFalseValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsNullValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsTrueValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsbnValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IsinValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\IssnValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\JsonValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LanguageValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LengthValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LessThanOrEqualValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LessThanValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LocaleValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\LuhnValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NoSuspiciousCharactersValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotBlankValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotCompromisedPasswordValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotEqualToValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotIdenticalToValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\NotNullValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\PasswordStrengthValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\RangeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\RegexValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\SequentiallyValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\TimeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\TimezoneValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\TypeValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\UlidValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\UniqueValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\UrlValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\UuidValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\ValidValidator' => true,
    'Symfony\\Component\\Validator\\Constraints\\WhenValidator' => true,
    'Symfony\\Component\\Validator\\Validator\\ValidatorInterface' => true,
    'Symfony\\Component\\WebLink\\HttpHeaderSerializer' => true,
    'Symfony\\Contracts\\Cache\\CacheInterface' => true,
    'Symfony\\Contracts\\Cache\\TagAwareCacheInterface' => true,
    'Symfony\\Contracts\\EventDispatcher\\EventDispatcherInterface' => true,
    'Symfony\\Contracts\\HttpClient\\HttpClientInterface' => true,
    'Symfony\\Contracts\\Translation\\LocaleAwareInterface' => true,
    'Symfony\\Contracts\\Translation\\TranslatorInterface' => true,
    'Symfony\\UX\\Turbo\\Broadcaster\\BroadcasterInterface' => true,
    'Twig\\Environment' => true,
    'Twig_Environment' => true,
    'api_platform.api.identifiers_extractor' => true,
    'api_platform.argument_resolver.payload' => true,
    'api_platform.cache.metadata.property' => true,
    'api_platform.cache.metadata.property.recorder_inner' => true,
    'api_platform.cache.metadata.resource' => true,
    'api_platform.cache.metadata.resource.recorder_inner' => true,
    'api_platform.cache.metadata.resource_collection' => true,
    'api_platform.cache.metadata.resource_collection.recorder_inner' => true,
    'api_platform.cache.route_name_resolver' => true,
    'api_platform.cache.route_name_resolver.recorder_inner' => true,
    'api_platform.cache_warmer.cache_pool_clearer' => true,
    'api_platform.data_collector.request' => true,
    'api_platform.doctrine.metadata_factory' => true,
    'api_platform.doctrine.orm.backed_enum_filter' => true,
    'api_platform.doctrine.orm.backed_enum_filter.instance' => true,
    'api_platform.doctrine.orm.boolean_filter' => true,
    'api_platform.doctrine.orm.boolean_filter.instance' => true,
    'api_platform.doctrine.orm.date_filter' => true,
    'api_platform.doctrine.orm.date_filter.instance' => true,
    'api_platform.doctrine.orm.exists_filter' => true,
    'api_platform.doctrine.orm.exists_filter.instance' => true,
    'api_platform.doctrine.orm.extension.parameter_extension' => true,
    'api_platform.doctrine.orm.links_handler' => true,
    'api_platform.doctrine.orm.metadata.property.metadata_factory' => true,
    'api_platform.doctrine.orm.metadata.property.metadata_factory.inner' => true,
    'api_platform.doctrine.orm.metadata.resource.link_factory' => true,
    'api_platform.doctrine.orm.metadata.resource.link_factory.inner' => true,
    'api_platform.doctrine.orm.metadata.resource.metadata_collection_factory' => true,
    'api_platform.doctrine.orm.metadata.resource.metadata_collection_factory.inner' => true,
    'api_platform.doctrine.orm.numeric_filter' => true,
    'api_platform.doctrine.orm.numeric_filter.instance' => true,
    'api_platform.doctrine.orm.order_filter' => true,
    'api_platform.doctrine.orm.query_extension.eager_loading' => true,
    'api_platform.doctrine.orm.query_extension.filter' => true,
    'api_platform.doctrine.orm.query_extension.filter_eager_loading' => true,
    'api_platform.doctrine.orm.query_extension.order' => true,
    'api_platform.doctrine.orm.query_extension.pagination' => true,
    'api_platform.doctrine.orm.range_filter' => true,
    'api_platform.doctrine.orm.range_filter.instance' => true,
    'api_platform.doctrine.orm.search_filter' => true,
    'api_platform.doctrine.orm.search_filter.instance' => true,
    'api_platform.doctrine.orm.state.collection_provider' => true,
    'api_platform.doctrine.orm.state.item_provider' => true,
    'api_platform.doctrine.orm.state.persist_processor' => true,
    'api_platform.doctrine.orm.state.remove_processor' => true,
    'api_platform.error_listener' => true,
    'api_platform.filter_locator' => true,
    'api_platform.http_cache.processor.add_headers' => true,
    'api_platform.http_cache.processor.add_headers.inner' => true,
    'api_platform.hydra.json_schema.schema_factory' => true,
    'api_platform.hydra.json_schema.schema_factory.inner' => true,
    'api_platform.hydra.name_converter.hydra_prefix' => true,
    'api_platform.hydra.name_converter.hydra_prefix.inner' => true,
    'api_platform.hydra.normalizer.collection' => true,
    'api_platform.hydra.normalizer.collection_filters' => true,
    'api_platform.hydra.normalizer.collection_filters.inner' => true,
    'api_platform.hydra.normalizer.constraint_violation_list' => true,
    'api_platform.hydra.normalizer.documentation' => true,
    'api_platform.hydra.normalizer.entrypoint' => true,
    'api_platform.hydra.normalizer.error' => true,
    'api_platform.hydra.normalizer.partial_collection_view' => true,
    'api_platform.hydra.normalizer.partial_collection_view.inner' => true,
    'api_platform.hydra.processor.link' => true,
    'api_platform.hydra.processor.link.inner' => true,
    'api_platform.identifiers_extractor' => true,
    'api_platform.inflector' => true,
    'api_platform.iri_converter' => true,
    'api_platform.json_schema.backward_compatible_schema_factory' => true,
    'api_platform.json_schema.backward_compatible_schema_factory.inner' => true,
    'api_platform.json_schema.definition_name_factory' => true,
    'api_platform.json_schema.json_schema_generate_command' => true,
    'api_platform.json_schema.metadata.property.metadata_factory.schema' => true,
    'api_platform.json_schema.metadata.property.metadata_factory.schema.inner' => true,
    'api_platform.json_schema.schema_factory' => true,
    'api_platform.json_schema.type_factory' => true,
    'api_platform.jsonld.context_builder' => true,
    'api_platform.jsonld.encoder' => true,
    'api_platform.jsonld.normalizer.error' => true,
    'api_platform.jsonld.normalizer.item' => true,
    'api_platform.jsonld.normalizer.object' => true,
    'api_platform.jsonld.normalizer.validation_exception' => true,
    'api_platform.jsonopenapi.encoder' => true,
    'api_platform.listener.exception' => true,
    'api_platform.listener.request.add_format' => true,
    'api_platform.listener.request.deserialize' => true,
    'api_platform.listener.request.read' => true,
    'api_platform.listener.view.respond' => true,
    'api_platform.listener.view.serialize' => true,
    'api_platform.listener.view.validate' => true,
    'api_platform.listener.view.validate_query_parameters' => true,
    'api_platform.listener.view.write' => true,
    'api_platform.maker.command.state_processor' => true,
    'api_platform.maker.command.state_provider' => true,
    'api_platform.message_bus' => true,
    'api_platform.messenger.metadata.resource.metadata_collection_factory' => true,
    'api_platform.messenger.metadata.resource.metadata_collection_factory.inner' => true,
    'api_platform.metadata.inflector' => true,
    'api_platform.metadata.operation.metadata_factory' => true,
    'api_platform.metadata.path_segment_name_generator.dash' => true,
    'api_platform.metadata.path_segment_name_generator.underscore' => true,
    'api_platform.metadata.property.metadata_factory' => true,
    'api_platform.metadata.property.metadata_factory.attribute' => true,
    'api_platform.metadata.property.metadata_factory.attribute.inner' => true,
    'api_platform.metadata.property.metadata_factory.cached' => true,
    'api_platform.metadata.property.metadata_factory.cached.inner' => true,
    'api_platform.metadata.property.metadata_factory.default_property' => true,
    'api_platform.metadata.property.metadata_factory.default_property.inner' => true,
    'api_platform.metadata.property.metadata_factory.identifier' => true,
    'api_platform.metadata.property.metadata_factory.identifier.inner' => true,
    'api_platform.metadata.property.metadata_factory.property_info' => true,
    'api_platform.metadata.property.metadata_factory.property_info.inner' => true,
    'api_platform.metadata.property.metadata_factory.serializer' => true,
    'api_platform.metadata.property.metadata_factory.serializer.inner' => true,
    'api_platform.metadata.property.metadata_factory.validator' => true,
    'api_platform.metadata.property.metadata_factory.validator.inner' => true,
    'api_platform.metadata.property.metadata_factory.xml' => true,
    'api_platform.metadata.property.metadata_factory.yaml' => true,
    'api_platform.metadata.property.metadata_factory.yaml.inner' => true,
    'api_platform.metadata.property.name_collection_factory' => true,
    'api_platform.metadata.property.name_collection_factory.cached' => true,
    'api_platform.metadata.property.name_collection_factory.cached.inner' => true,
    'api_platform.metadata.property.name_collection_factory.concerns' => true,
    'api_platform.metadata.property.name_collection_factory.concerns.inner' => true,
    'api_platform.metadata.property.name_collection_factory.property_info' => true,
    'api_platform.metadata.property.name_collection_factory.xml' => true,
    'api_platform.metadata.property.name_collection_factory.xml.inner' => true,
    'api_platform.metadata.property.name_collection_factory.yaml' => true,
    'api_platform.metadata.property.name_collection_factory.yaml.inner' => true,
    'api_platform.metadata.property_extractor.xml' => true,
    'api_platform.metadata.property_extractor.yaml' => true,
    'api_platform.metadata.property_schema.choice_restriction' => true,
    'api_platform.metadata.property_schema.collection_restriction' => true,
    'api_platform.metadata.property_schema.count_restriction' => true,
    'api_platform.metadata.property_schema.format_restriction' => true,
    'api_platform.metadata.property_schema.greater_than_or_equal_restriction' => true,
    'api_platform.metadata.property_schema.greater_than_restriction' => true,
    'api_platform.metadata.property_schema.length_restriction' => true,
    'api_platform.metadata.property_schema.less_than_or_equal_restriction' => true,
    'api_platform.metadata.property_schema.less_than_restriction' => true,
    'api_platform.metadata.property_schema.one_of_restriction' => true,
    'api_platform.metadata.property_schema.range_restriction' => true,
    'api_platform.metadata.property_schema.regex_restriction' => true,
    'api_platform.metadata.property_schema.unique_restriction' => true,
    'api_platform.metadata.resource.link_factory' => true,
    'api_platform.metadata.resource.metadata_collection_factory' => true,
    'api_platform.metadata.resource.metadata_collection_factory.alternate_uri' => true,
    'api_platform.metadata.resource.metadata_collection_factory.alternate_uri.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.attributes' => true,
    'api_platform.metadata.resource.metadata_collection_factory.backed_enum' => true,
    'api_platform.metadata.resource.metadata_collection_factory.backed_enum.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.cached' => true,
    'api_platform.metadata.resource.metadata_collection_factory.cached.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.concerns' => true,
    'api_platform.metadata.resource.metadata_collection_factory.concerns.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.deprecations' => true,
    'api_platform.metadata.resource.metadata_collection_factory.deprecations.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.filters' => true,
    'api_platform.metadata.resource.metadata_collection_factory.filters.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.formats' => true,
    'api_platform.metadata.resource.metadata_collection_factory.formats.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.input_output' => true,
    'api_platform.metadata.resource.metadata_collection_factory.input_output.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.link' => true,
    'api_platform.metadata.resource.metadata_collection_factory.link.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.main_controller' => true,
    'api_platform.metadata.resource.metadata_collection_factory.main_controller.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.not_exposed_operation' => true,
    'api_platform.metadata.resource.metadata_collection_factory.not_exposed_operation.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.operation_name' => true,
    'api_platform.metadata.resource.metadata_collection_factory.operation_name.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.parameter' => true,
    'api_platform.metadata.resource.metadata_collection_factory.parameter.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.php_doc' => true,
    'api_platform.metadata.resource.metadata_collection_factory.php_doc.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.uri_template' => true,
    'api_platform.metadata.resource.metadata_collection_factory.uri_template.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.xml' => true,
    'api_platform.metadata.resource.metadata_collection_factory.xml.inner' => true,
    'api_platform.metadata.resource.metadata_collection_factory.yaml' => true,
    'api_platform.metadata.resource.metadata_collection_factory.yaml.inner' => true,
    'api_platform.metadata.resource.name_collection_factory' => true,
    'api_platform.metadata.resource.name_collection_factory.attributes' => true,
    'api_platform.metadata.resource.name_collection_factory.attributes.inner' => true,
    'api_platform.metadata.resource.name_collection_factory.cached' => true,
    'api_platform.metadata.resource.name_collection_factory.cached.inner' => true,
    'api_platform.metadata.resource.name_collection_factory.class_name' => true,
    'api_platform.metadata.resource.name_collection_factory.class_name.inner' => true,
    'api_platform.metadata.resource.name_collection_factory.concerns' => true,
    'api_platform.metadata.resource.name_collection_factory.concerns.inner' => true,
    'api_platform.metadata.resource.name_collection_factory.xml' => true,
    'api_platform.metadata.resource.name_collection_factory.yaml' => true,
    'api_platform.metadata.resource.name_collection_factory.yaml.inner' => true,
    'api_platform.metadata.resource_extractor.xml' => true,
    'api_platform.metadata.resource_extractor.yaml' => true,
    'api_platform.name_converter' => true,
    'api_platform.negotiator' => true,
    'api_platform.normalizer.constraint_violation_list' => true,
    'api_platform.openapi.command' => true,
    'api_platform.openapi.factory' => true,
    'api_platform.openapi.normalizer' => true,
    'api_platform.openapi.normalizer.api_gateway' => true,
    'api_platform.openapi.normalizer.api_gateway.inner' => true,
    'api_platform.openapi.normalizer.legacy' => true,
    'api_platform.openapi.normalizer.legacy.inner' => true,
    'api_platform.openapi.options' => true,
    'api_platform.openapi.provider' => true,
    'api_platform.openapi.serializer_context_builder' => true,
    'api_platform.openapi.serializer_context_builder.inner' => true,
    'api_platform.pagination' => true,
    'api_platform.pagination_options' => true,
    'api_platform.path_segment_name_generator' => true,
    'api_platform.path_segment_name_generator.dash' => true,
    'api_platform.path_segment_name_generator.underscore' => true,
    'api_platform.problem.encoder' => true,
    'api_platform.problem.normalizer.constraint_violation_list' => true,
    'api_platform.problem.normalizer.error' => true,
    'api_platform.problem.normalizer.validation_exception' => true,
    'api_platform.property_accessor' => true,
    'api_platform.property_info' => true,
    'api_platform.resource_class_resolver' => true,
    'api_platform.route_loader' => true,
    'api_platform.router' => true,
    'api_platform.security.expression_language' => true,
    'api_platform.security.expression_language_provider' => true,
    'api_platform.security.resource_access_checker' => true,
    'api_platform.serializer' => true,
    'api_platform.serializer.context_builder' => true,
    'api_platform.serializer.context_builder.filter' => true,
    'api_platform.serializer.context_builder.filter.inner' => true,
    'api_platform.serializer.filter_parameter_provider' => true,
    'api_platform.serializer.group_filter' => true,
    'api_platform.serializer.mapping.class_metadata_factory' => true,
    'api_platform.serializer.mapping.class_metadata_factory.inner' => true,
    'api_platform.serializer.normalizer.item' => true,
    'api_platform.serializer.property_filter' => true,
    'api_platform.serializer_locator' => true,
    'api_platform.state.error_provider' => true,
    'api_platform.state.item_provider' => true,
    'api_platform.state_processor.add_link_header' => true,
    'api_platform.state_processor.add_link_header.inner' => true,
    'api_platform.state_processor.documentation' => true,
    'api_platform.state_processor.documentation.serialize' => true,
    'api_platform.state_processor.documentation.serialize.inner' => true,
    'api_platform.state_processor.documentation.write' => true,
    'api_platform.state_processor.documentation.write.inner' => true,
    'api_platform.state_processor.locator' => true,
    'api_platform.state_processor.respond' => true,
    'api_platform.state_processor.serialize' => true,
    'api_platform.state_processor.write' => true,
    'api_platform.state_provider' => true,
    'api_platform.state_provider.access_checker' => true,
    'api_platform.state_provider.access_checker.inner' => true,
    'api_platform.state_provider.access_checker.post_deserialize' => true,
    'api_platform.state_provider.access_checker.post_deserialize.inner' => true,
    'api_platform.state_provider.access_checker.post_validate' => true,
    'api_platform.state_provider.access_checker.post_validate.inner' => true,
    'api_platform.state_provider.content_negotiation' => true,
    'api_platform.state_provider.create' => true,
    'api_platform.state_provider.deserialize' => true,
    'api_platform.state_provider.documentation' => true,
    'api_platform.state_provider.documentation.content_negotiation' => true,
    'api_platform.state_provider.documentation.content_negotiation.inner' => true,
    'api_platform.state_provider.documentation.read' => true,
    'api_platform.state_provider.documentation.read.inner' => true,
    'api_platform.state_provider.locator' => true,
    'api_platform.state_provider.object' => true,
    'api_platform.state_provider.parameter' => true,
    'api_platform.state_provider.parameter.inner' => true,
    'api_platform.state_provider.parameter_validator.inner' => true,
    'api_platform.state_provider.read' => true,
    'api_platform.state_provider.security_parameter' => true,
    'api_platform.state_provider.security_parameter.inner' => true,
    'api_platform.state_provider.validate' => true,
    'api_platform.swagger_ui.context' => true,
    'api_platform.swagger_ui.documentation.provider' => true,
    'api_platform.swagger_ui.documentation.provider.inner' => true,
    'api_platform.swagger_ui.processor' => true,
    'api_platform.swagger_ui.provider' => true,
    'api_platform.swagger_ui.provider.inner' => true,
    'api_platform.symfony.iri_converter' => true,
    'api_platform.symfony.iri_converter.skolem' => true,
    'api_platform.uri_variables.converter' => true,
    'api_platform.uri_variables.transformer.date_time' => true,
    'api_platform.uri_variables.transformer.integer' => true,
    'api_platform.validator' => true,
    'api_platform.validator.query_parameter_validator' => true,
    'api_platform.validator.state.error_provider' => true,
    'api_platform.yamlopenapi.encoder' => true,
    'argument_metadata_factory' => true,
    'argument_resolver' => true,
    'argument_resolver.backed_enum_resolver' => true,
    'argument_resolver.controller_locator' => true,
    'argument_resolver.datetime' => true,
    'argument_resolver.default' => true,
    'argument_resolver.not_tagged_controller' => true,
    'argument_resolver.query_parameter_value_resolver' => true,
    'argument_resolver.request' => true,
    'argument_resolver.request_attribute' => true,
    'argument_resolver.request_payload' => true,
    'argument_resolver.service' => true,
    'argument_resolver.session' => true,
    'argument_resolver.variadic' => true,
    'asset_mapper' => true,
    'asset_mapper.asset_package' => true,
    'asset_mapper.asset_package.inner' => true,
    'asset_mapper.cached_mapped_asset_factory' => true,
    'asset_mapper.cached_mapped_asset_factory.inner' => true,
    'asset_mapper.command.compile' => true,
    'asset_mapper.command.debug' => true,
    'asset_mapper.compiled_asset_mapper_config_reader' => true,
    'asset_mapper.compiler.css_asset_url_compiler' => true,
    'asset_mapper.compiler.javascript_import_path_compiler' => true,
    'asset_mapper.compiler.source_mapping_urls_compiler' => true,
    'asset_mapper.dev_server_subscriber' => true,
    'asset_mapper.http_client' => true,
    'asset_mapper.importmap.auditor' => true,
    'asset_mapper.importmap.command.audit' => true,
    'asset_mapper.importmap.command.install' => true,
    'asset_mapper.importmap.command.outdated' => true,
    'asset_mapper.importmap.command.remove' => true,
    'asset_mapper.importmap.command.require' => true,
    'asset_mapper.importmap.command.update' => true,
    'asset_mapper.importmap.config_reader' => true,
    'asset_mapper.importmap.generator' => true,
    'asset_mapper.importmap.manager' => true,
    'asset_mapper.importmap.remote_package_downloader' => true,
    'asset_mapper.importmap.remote_package_storage' => true,
    'asset_mapper.importmap.renderer' => true,
    'asset_mapper.importmap.resolver' => true,
    'asset_mapper.importmap.update_checker' => true,
    'asset_mapper.importmap.version_checker' => true,
    'asset_mapper.local_public_assets_filesystem' => true,
    'asset_mapper.mapped_asset_factory' => true,
    'asset_mapper.public_assets_path_resolver' => true,
    'asset_mapper.repository' => true,
    'asset_mapper_compiler' => true,
    'assets._default_package' => true,
    'assets.context' => true,
    'assets.empty_package' => true,
    'assets.empty_version_strategy' => true,
    'assets.json_manifest_version_strategy' => true,
    'assets.packages' => true,
    'assets.path_package' => true,
    'assets.static_version_strategy' => true,
    'assets.url_package' => true,
    'cache.adapter.apcu' => true,
    'cache.adapter.array' => true,
    'cache.adapter.doctrine_dbal' => true,
    'cache.adapter.filesystem' => true,
    'cache.adapter.memcached' => true,
    'cache.adapter.pdo' => true,
    'cache.adapter.psr6' => true,
    'cache.adapter.redis' => true,
    'cache.adapter.redis_tag_aware' => true,
    'cache.adapter.system' => true,
    'cache.annotations' => true,
    'cache.annotations.recorder_inner' => true,
    'cache.app.recorder_inner' => true,
    'cache.app.taggable' => true,
    'cache.asset_mapper' => true,
    'cache.asset_mapper.recorder_inner' => true,
    'cache.default_clearer' => true,
    'cache.default_doctrine_dbal_provider' => true,
    'cache.default_marshaller' => true,
    'cache.default_memcached_provider' => true,
    'cache.default_redis_provider' => true,
    'cache.doctrine.orm.default.metadata' => true,
    'cache.doctrine.orm.default.query' => true,
    'cache.doctrine.orm.default.query.recorder_inner' => true,
    'cache.doctrine.orm.default.result' => true,
    'cache.doctrine.orm.default.result.recorder_inner' => true,
    'cache.early_expiration_handler' => true,
    'cache.messenger.restart_workers_signal' => true,
    'cache.messenger.restart_workers_signal.recorder_inner' => true,
    'cache.property_access' => true,
    'cache.property_info' => true,
    'cache.property_info.recorder_inner' => true,
    'cache.security_expression_language' => true,
    'cache.security_expression_language.recorder_inner' => true,
    'cache.security_is_granted_attribute_expression_language.recorder_inner' => true,
    'cache.serializer' => true,
    'cache.serializer.recorder_inner' => true,
    'cache.system.recorder_inner' => true,
    'cache.validator' => true,
    'cache.validator.recorder_inner' => true,
    'cache.validator_expression_language.recorder_inner' => true,
    'cache_clearer' => true,
    'cache_pool_clearer.cache_warmer' => true,
    'chatter.messenger.chat_handler' => true,
    'chatter.transport_factory' => true,
    'chatter.transports' => true,
    'clock' => true,
    'config.resource.self_checking_resource_checker' => true,
    'config_builder.warmer' => true,
    'config_cache_factory' => true,
    'console.command.about' => true,
    'console.command.assets_install' => true,
    'console.command.cache_clear' => true,
    'console.command.cache_pool_clear' => true,
    'console.command.cache_pool_delete' => true,
    'console.command.cache_pool_invalidate_tags' => true,
    'console.command.cache_pool_list' => true,
    'console.command.cache_pool_prune' => true,
    'console.command.cache_warmup' => true,
    'console.command.config_debug' => true,
    'console.command.config_dump_reference' => true,
    'console.command.container_debug' => true,
    'console.command.container_lint' => true,
    'console.command.debug_autowiring' => true,
    'console.command.dotenv_debug' => true,
    'console.command.event_dispatcher_debug' => true,
    'console.command.form_debug' => true,
    'console.command.mailer_test' => true,
    'console.command.messenger_consume_messages' => true,
    'console.command.messenger_debug' => true,
    'console.command.messenger_failed_messages_remove' => true,
    'console.command.messenger_failed_messages_retry' => true,
    'console.command.messenger_failed_messages_show' => true,
    'console.command.messenger_setup_transports' => true,
    'console.command.messenger_stats' => true,
    'console.command.messenger_stop_workers' => true,
    'console.command.router_debug' => true,
    'console.command.router_match' => true,
    'console.command.secrets_decrypt_to_local' => true,
    'console.command.secrets_encrypt_from_local' => true,
    'console.command.secrets_generate_key' => true,
    'console.command.secrets_list' => true,
    'console.command.secrets_remove' => true,
    'console.command.secrets_set' => true,
    'console.command.serializer_debug' => true,
    'console.command.translation_debug' => true,
    'console.command.translation_extract' => true,
    'console.command.translation_pull' => true,
    'console.command.translation_push' => true,
    'console.command.validator_debug' => true,
    'console.command.xliff_lint' => true,
    'console.command.yaml_lint' => true,
    'console.error_listener' => true,
    'console.messenger.application' => true,
    'console.messenger.execute_command_handler' => true,
    'console.suggest_missing_package_subscriber' => true,
    'console_profiler_listener' => true,
    'container.env' => true,
    'container.env_var_processor' => true,
    'container.getenv' => true,
    'controller.cache_attribute_listener' => true,
    'controller.is_granted_attribute_listener' => true,
    'controller.template_attribute_listener' => true,
    'controller_resolver' => true,
    'data_collector.ajax' => true,
    'data_collector.config' => true,
    'data_collector.doctrine' => true,
    'data_collector.events' => true,
    'data_collector.exception' => true,
    'data_collector.form' => true,
    'data_collector.form.extractor' => true,
    'data_collector.http_client' => true,
    'data_collector.logger' => true,
    'data_collector.memory' => true,
    'data_collector.messenger' => true,
    'data_collector.request' => true,
    'data_collector.request.session_collector' => true,
    'data_collector.router' => true,
    'data_collector.security' => true,
    'data_collector.time' => true,
    'data_collector.translation' => true,
    'data_collector.twig' => true,
    'data_collector.validator' => true,
    'debug.api_platform.debug_resource.command' => true,
    'debug.argument_resolver' => true,
    'debug.argument_resolver.inner' => true,
    'debug.controller_resolver' => true,
    'debug.controller_resolver.inner' => true,
    'debug.debug_handlers_listener' => true,
    'debug.debug_logger_configurator' => true,
    'debug.dump_listener' => true,
    'debug.event_dispatcher' => true,
    'debug.event_dispatcher.inner' => true,
    'debug.file_link_formatter' => true,
    'debug.file_link_formatter.url_format' => true,
    'debug.log_processor' => true,
    'debug.security.access.decision_manager' => true,
    'debug.security.access.decision_manager.inner' => true,
    'debug.security.event_dispatcher.api' => true,
    'debug.security.event_dispatcher.api.inner' => true,
    'debug.security.event_dispatcher.login' => true,
    'debug.security.event_dispatcher.login.inner' => true,
    'debug.security.event_dispatcher.main' => true,
    'debug.security.event_dispatcher.main.inner' => true,
    'debug.security.firewall' => true,
    'debug.security.firewall.authenticator.api' => true,
    'debug.security.firewall.authenticator.api.inner' => true,
    'debug.security.firewall.authenticator.login' => true,
    'debug.security.firewall.authenticator.login.inner' => true,
    'debug.security.firewall.authenticator.main' => true,
    'debug.security.firewall.authenticator.main.inner' => true,
    'debug.security.voter.vote_listener' => true,
    'debug.serializer' => true,
    'debug.serializer.inner' => true,
    'debug.traced.messenger.bus.default' => true,
    'debug.traced.messenger.bus.default.inner' => true,
    'debug.validator' => true,
    'debug.validator.inner' => true,
    'debug.var_dumper.cli_dumper' => true,
    'debug.var_dumper.cloner' => true,
    'dependency_injection.config.container_parameters_resource_checker' => true,
    'disallow_search_engine_index_response_listener' => true,
    'doctrine.cache_clear_metadata_command' => true,
    'doctrine.cache_clear_query_cache_command' => true,
    'doctrine.cache_clear_result_command' => true,
    'doctrine.cache_collection_region_command' => true,
    'doctrine.clear_entity_region_command' => true,
    'doctrine.clear_query_region_command' => true,
    'doctrine.database_create_command' => true,
    'doctrine.database_drop_command' => true,
    'doctrine.dbal.connection' => true,
    'doctrine.dbal.connection.configuration' => true,
    'doctrine.dbal.connection.event_manager' => true,
    'doctrine.dbal.connection_expiries' => true,
    'doctrine.dbal.connection_factory' => true,
    'doctrine.dbal.connection_factory.dsn_parser' => true,
    'doctrine.dbal.debug_middleware' => true,
    'doctrine.dbal.debug_middleware.default' => true,
    'doctrine.dbal.default_connection.configuration' => true,
    'doctrine.dbal.default_connection.event_manager' => true,
    'doctrine.dbal.default_schema_asset_filter_manager' => true,
    'doctrine.dbal.default_schema_manager_factory' => true,
    'doctrine.dbal.event_manager' => true,
    'doctrine.dbal.legacy_schema_manager_factory' => true,
    'doctrine.dbal.logging_middleware' => true,
    'doctrine.dbal.logging_middleware.default' => true,
    'doctrine.dbal.schema_asset_filter_manager' => true,
    'doctrine.dbal.well_known_schema_asset_filter' => true,
    'doctrine.debug_data_holder' => true,
    'doctrine.id_generator_locator' => true,
    'doctrine.mapping_info_command' => true,
    'doctrine.migrations.configuration' => true,
    'doctrine.migrations.configuration_loader' => true,
    'doctrine.migrations.connection_loader' => true,
    'doctrine.migrations.connection_registry_loader' => true,
    'doctrine.migrations.container_aware_migrations_factory' => true,
    'doctrine.migrations.container_aware_migrations_factory.inner' => true,
    'doctrine.migrations.dependency_factory' => true,
    'doctrine.migrations.em_loader' => true,
    'doctrine.migrations.entity_manager_registry_loader' => true,
    'doctrine.migrations.metadata_storage' => true,
    'doctrine.migrations.migrations_factory' => true,
    'doctrine.migrations.storage.table_storage' => true,
    'doctrine.orm.command.entity_manager_provider' => true,
    'doctrine.orm.configuration' => true,
    'doctrine.orm.container_repository_factory' => true,
    'doctrine.orm.default_attribute_metadata_driver' => true,
    'doctrine.orm.default_configuration' => true,
    'doctrine.orm.default_entity_listener_resolver' => true,
    'doctrine.orm.default_entity_manager.event_manager' => true,
    'doctrine.orm.default_entity_manager.property_info_extractor' => true,
    'doctrine.orm.default_entity_manager.validator_loader' => true,
    'doctrine.orm.default_listeners.attach_entity_listeners' => true,
    'doctrine.orm.default_manager_configurator' => true,
    'doctrine.orm.default_metadata_cache' => true,
    'doctrine.orm.default_metadata_driver' => true,
    'doctrine.orm.default_query_cache' => true,
    'doctrine.orm.default_result_cache' => true,
    'doctrine.orm.entity_manager.abstract' => true,
    'doctrine.orm.entity_value_resolver' => true,
    'doctrine.orm.entity_value_resolver.expression_language' => true,
    'doctrine.orm.listeners.doctrine_dbal_cache_adapter_schema_listener' => true,
    'doctrine.orm.listeners.doctrine_token_provider_schema_listener' => true,
    'doctrine.orm.listeners.lock_store_schema_listener' => true,
    'doctrine.orm.listeners.pdo_session_handler_schema_listener' => true,
    'doctrine.orm.listeners.resolve_target_entity' => true,
    'doctrine.orm.manager_configurator.abstract' => true,
    'doctrine.orm.messenger.doctrine_schema_listener' => true,
    'doctrine.orm.messenger.event_subscriber.doctrine_clear_entity_manager' => true,
    'doctrine.orm.naming_strategy.default' => true,
    'doctrine.orm.naming_strategy.underscore' => true,
    'doctrine.orm.naming_strategy.underscore_number_aware' => true,
    'doctrine.orm.proxy_cache_warmer' => true,
    'doctrine.orm.quote_strategy.ansi' => true,
    'doctrine.orm.quote_strategy.default' => true,
    'doctrine.orm.security.user.provider' => true,
    'doctrine.orm.typed_field_mapper.default' => true,
    'doctrine.orm.validator.unique' => true,
    'doctrine.orm.validator_initializer' => true,
    'doctrine.query_dql_command' => true,
    'doctrine.query_sql_command' => true,
    'doctrine.schema_create_command' => true,
    'doctrine.schema_drop_command' => true,
    'doctrine.schema_update_command' => true,
    'doctrine.schema_validate_command' => true,
    'doctrine.twig.doctrine_extension' => true,
    'doctrine.ulid_generator' => true,
    'doctrine.uuid_generator' => true,
    'doctrine_migrations.current_command' => true,
    'doctrine_migrations.diff_command' => true,
    'doctrine_migrations.dump_schema_command' => true,
    'doctrine_migrations.execute_command' => true,
    'doctrine_migrations.generate_command' => true,
    'doctrine_migrations.latest_command' => true,
    'doctrine_migrations.migrate_command' => true,
    'doctrine_migrations.rollup_command' => true,
    'doctrine_migrations.schema_filter_listener' => true,
    'doctrine_migrations.status_command' => true,
    'doctrine_migrations.sync_metadata_command' => true,
    'doctrine_migrations.up_to_date_command' => true,
    'doctrine_migrations.version_command' => true,
    'doctrine_migrations.versions_command' => true,
    'error_handler.error_renderer.html' => true,
    'error_handler.error_renderer.serializer' => true,
    'error_renderer' => true,
    'error_renderer.html' => true,
    'error_renderer.serializer' => true,
    'exception_listener' => true,
    'file_locator' => true,
    'filesystem' => true,
    'form.choice_list_factory' => true,
    'form.choice_list_factory.cached' => true,
    'form.choice_list_factory.default' => true,
    'form.choice_list_factory.property_access' => true,
    'form.extension' => true,
    'form.factory' => true,
    'form.listener.password_hasher' => true,
    'form.property_accessor' => true,
    'form.registry' => true,
    'form.resolved_type_factory' => true,
    'form.server_params' => true,
    'form.type.choice' => true,
    'form.type.color' => true,
    'form.type.entity' => true,
    'form.type.file' => true,
    'form.type.form' => true,
    'form.type_extension.csrf' => true,
    'form.type_extension.form.data_collector' => true,
    'form.type_extension.form.http_foundation' => true,
    'form.type_extension.form.password_hasher' => true,
    'form.type_extension.form.request_handler' => true,
    'form.type_extension.form.transformation_failure_handling' => true,
    'form.type_extension.form.validator' => true,
    'form.type_extension.password.password_hasher' => true,
    'form.type_extension.repeated.validator' => true,
    'form.type_extension.submit.validator' => true,
    'form.type_extension.upload.validator' => true,
    'form.type_guesser.doctrine' => true,
    'form.type_guesser.validator' => true,
    'fragment.handler' => true,
    'fragment.renderer.inline' => true,
    'fragment.uri_generator' => true,
    'http_cache' => true,
    'http_cache.store' => true,
    'http_client' => true,
    'http_client.abstract_retry_strategy' => true,
    'http_client.messenger.ping_webhook_handler' => true,
    'http_client.transport' => true,
    'http_client.uri_template' => true,
    'http_client.uri_template.inner' => true,
    'http_client.uri_template_expander.guzzle' => true,
    'http_client.uri_template_expander.rize' => true,
    'identity_translator' => true,
    'locale_aware_listener' => true,
    'locale_listener' => true,
    'logger' => true,
    'mailer' => true,
    'mailer.data_collector' => true,
    'mailer.default_transport' => true,
    'mailer.envelope_listener' => true,
    'mailer.mailer' => true,
    'mailer.message_logger_listener' => true,
    'mailer.messenger.message_handler' => true,
    'mailer.messenger_transport_listener' => true,
    'mailer.transport_factory' => true,
    'mailer.transport_factory.abstract' => true,
    'mailer.transport_factory.gmail' => true,
    'mailer.transport_factory.native' => true,
    'mailer.transport_factory.null' => true,
    'mailer.transport_factory.sendmail' => true,
    'mailer.transport_factory.smtp' => true,
    'mailer.transports' => true,
    'maker.auto_command.abstract' => true,
    'maker.auto_command.make_auth' => true,
    'maker.auto_command.make_command' => true,
    'maker.auto_command.make_controller' => true,
    'maker.auto_command.make_crud' => true,
    'maker.auto_command.make_docker_database' => true,
    'maker.auto_command.make_entity' => true,
    'maker.auto_command.make_fixtures' => true,
    'maker.auto_command.make_form' => true,
    'maker.auto_command.make_listener' => true,
    'maker.auto_command.make_message' => true,
    'maker.auto_command.make_messenger_middleware' => true,
    'maker.auto_command.make_migration' => true,
    'maker.auto_command.make_registration_form' => true,
    'maker.auto_command.make_reset_password' => true,
    'maker.auto_command.make_schedule' => true,
    'maker.auto_command.make_security_custom' => true,
    'maker.auto_command.make_security_form_login' => true,
    'maker.auto_command.make_serializer_encoder' => true,
    'maker.auto_command.make_serializer_normalizer' => true,
    'maker.auto_command.make_state_processor' => true,
    'maker.auto_command.make_state_provider' => true,
    'maker.auto_command.make_stimulus_controller' => true,
    'maker.auto_command.make_test' => true,
    'maker.auto_command.make_twig_component' => true,
    'maker.auto_command.make_twig_extension' => true,
    'maker.auto_command.make_user' => true,
    'maker.auto_command.make_validator' => true,
    'maker.auto_command.make_voter' => true,
    'maker.auto_command.make_webhook' => true,
    'maker.autoloader_finder' => true,
    'maker.autoloader_util' => true,
    'maker.console_error_listener' => true,
    'maker.doctrine_helper' => true,
    'maker.entity_class_generator' => true,
    'maker.event_registry' => true,
    'maker.file_link_formatter' => true,
    'maker.file_manager' => true,
    'maker.generator' => true,
    'maker.maker.make_authenticator' => true,
    'maker.maker.make_command' => true,
    'maker.maker.make_controller' => true,
    'maker.maker.make_crud' => true,
    'maker.maker.make_custom_authenticator' => true,
    'maker.maker.make_docker_database' => true,
    'maker.maker.make_entity' => true,
    'maker.maker.make_fixtures' => true,
    'maker.maker.make_form' => true,
    'maker.maker.make_form_login' => true,
    'maker.maker.make_functional_test' => true,
    'maker.maker.make_listener' => true,
    'maker.maker.make_message' => true,
    'maker.maker.make_messenger_middleware' => true,
    'maker.maker.make_migration' => true,
    'maker.maker.make_registration_form' => true,
    'maker.maker.make_reset_password' => true,
    'maker.maker.make_schedule' => true,
    'maker.maker.make_serializer_encoder' => true,
    'maker.maker.make_serializer_normalizer' => true,
    'maker.maker.make_stimulus_controller' => true,
    'maker.maker.make_subscriber' => true,
    'maker.maker.make_test' => true,
    'maker.maker.make_twig_component' => true,
    'maker.maker.make_twig_extension' => true,
    'maker.maker.make_unit_test' => true,
    'maker.maker.make_user' => true,
    'maker.maker.make_validator' => true,
    'maker.maker.make_voter' => true,
    'maker.maker.make_webhook' => true,
    'maker.php_compat_util' => true,
    'maker.renderer.form_type_renderer' => true,
    'maker.security_config_updater' => true,
    'maker.security_controller_builder' => true,
    'maker.template_component_generator' => true,
    'maker.template_linter' => true,
    'maker.user_class_builder' => true,
    'messenger.bus.default' => true,
    'messenger.bus.default.messenger.handlers_locator' => true,
    'messenger.bus.default.middleware.add_bus_name_stamp_middleware' => true,
    'messenger.bus.default.middleware.handle_message' => true,
    'messenger.bus.default.middleware.send_message' => true,
    'messenger.bus.default.middleware.traceable' => true,
    'messenger.default_serializer' => true,
    'messenger.failure.add_error_details_stamp_listener' => true,
    'messenger.failure.send_failed_message_to_failure_transport_listener' => true,
    'messenger.failure_transports.default' => true,
    'messenger.listener.dispatch_pcntl_signal_listener' => true,
    'messenger.listener.reset_services' => true,
    'messenger.listener.stop_worker_on_restart_signal_listener' => true,
    'messenger.listener.stop_worker_on_sigterm_signal_listener' => true,
    'messenger.listener.stop_worker_on_stop_exception_listener' => true,
    'messenger.listener.stop_worker_signals_listener' => true,
    'messenger.middleware.add_bus_name_stamp_middleware' => true,
    'messenger.middleware.dispatch_after_current_bus' => true,
    'messenger.middleware.doctrine_close_connection' => true,
    'messenger.middleware.doctrine_open_transaction_logger' => true,
    'messenger.middleware.doctrine_ping_connection' => true,
    'messenger.middleware.doctrine_transaction' => true,
    'messenger.middleware.failed_message_processing_middleware' => true,
    'messenger.middleware.handle_message' => true,
    'messenger.middleware.reject_redelivered_message_middleware' => true,
    'messenger.middleware.router_context' => true,
    'messenger.middleware.send_message' => true,
    'messenger.middleware.traceable' => true,
    'messenger.middleware.validation' => true,
    'messenger.receiver_locator' => true,
    'messenger.redispatch_message_handler' => true,
    'messenger.retry.abstract_multiplier_retry_strategy' => true,
    'messenger.retry.multiplier_retry_strategy.async' => true,
    'messenger.retry.multiplier_retry_strategy.failed' => true,
    'messenger.retry.send_failed_message_for_retry_listener' => true,
    'messenger.retry_strategy_locator' => true,
    'messenger.routable_message_bus' => true,
    'messenger.senders_locator' => true,
    'messenger.transport.amqp.factory' => true,
    'messenger.transport.async' => true,
    'messenger.transport.beanstalkd.factory' => true,
    'messenger.transport.doctrine.factory' => true,
    'messenger.transport.failed' => true,
    'messenger.transport.in_memory.factory' => true,
    'messenger.transport.native_php_serializer' => true,
    'messenger.transport.redis.factory' => true,
    'messenger.transport.sqs.factory' => true,
    'messenger.transport.symfony_serializer' => true,
    'messenger.transport.sync.factory' => true,
    'messenger.transport_factory' => true,
    'mime_types' => true,
    'monolog.activation_strategy.not_found' => true,
    'monolog.command.server_log' => true,
    'monolog.formatter.chrome_php' => true,
    'monolog.formatter.gelf_message' => true,
    'monolog.formatter.html' => true,
    'monolog.formatter.json' => true,
    'monolog.formatter.line' => true,
    'monolog.formatter.loggly' => true,
    'monolog.formatter.logstash' => true,
    'monolog.formatter.normalizer' => true,
    'monolog.formatter.scalar' => true,
    'monolog.formatter.wildfire' => true,
    'monolog.handler.chatbot' => true,
    'monolog.handler.console' => true,
    'monolog.handler.fingers_crossed.error_level_activation_strategy' => true,
    'monolog.handler.main' => true,
    'monolog.handler.null_internal' => true,
    'monolog.http_client' => true,
    'monolog.logger' => true,
    'monolog.logger.asset_mapper' => true,
    'monolog.logger.cache' => true,
    'monolog.logger.console' => true,
    'monolog.logger.debug' => true,
    'monolog.logger.doctrine' => true,
    'monolog.logger.event' => true,
    'monolog.logger.http_client' => true,
    'monolog.logger.mailer' => true,
    'monolog.logger.messenger' => true,
    'monolog.logger.php' => true,
    'monolog.logger.profiler' => true,
    'monolog.logger.request' => true,
    'monolog.logger.router' => true,
    'monolog.logger.security' => true,
    'monolog.logger.translation' => true,
    'monolog.logger_prototype' => true,
    'monolog.processor.psr_log_message' => true,
    'nelmio_cors.cacheable_response_vary_listener' => true,
    'nelmio_cors.cors_listener' => true,
    'nelmio_cors.options_provider.config' => true,
    'nelmio_cors.options_resolver' => true,
    'notifier' => true,
    'notifier.admin_recipient.0' => true,
    'notifier.channel.browser' => true,
    'notifier.channel.chat' => true,
    'notifier.channel.email' => true,
    'notifier.channel.push' => true,
    'notifier.channel.sms' => true,
    'notifier.channel_policy' => true,
    'notifier.data_collector' => true,
    'notifier.failed_message_listener' => true,
    'notifier.flash_message_importance_mapper' => true,
    'notifier.logger_notification_listener' => true,
    'notifier.monolog_handler' => true,
    'notifier.notification_logger_listener' => true,
    'notifier.transport_factory.abstract' => true,
    'notifier.transport_factory.null' => true,
    'parameter_bag' => true,
    'process.messenger.process_message_handler' => true,
    'profiler.storage' => true,
    'profiler_listener' => true,
    'property_accessor' => true,
    'property_info' => true,
    'property_info.php_doc_extractor' => true,
    'property_info.phpstan_extractor' => true,
    'property_info.reflection_extractor' => true,
    'property_info.serializer_extractor' => true,
    'response_listener' => true,
    'reverse_container' => true,
    'router.cache_warmer' => true,
    'router.default' => true,
    'router.expression_language_provider' => true,
    'router.request_context' => true,
    'router_listener' => true,
    'routing.loader.annotation' => true,
    'routing.loader.annotation.directory' => true,
    'routing.loader.annotation.file' => true,
    'routing.loader.attribute' => true,
    'routing.loader.attribute.directory' => true,
    'routing.loader.attribute.file' => true,
    'routing.loader.container' => true,
    'routing.loader.directory' => true,
    'routing.loader.glob' => true,
    'routing.loader.php' => true,
    'routing.loader.psr4' => true,
    'routing.loader.xml' => true,
    'routing.loader.yml' => true,
    'routing.resolver' => true,
    'secrets.decryption_key' => true,
    'secrets.local_vault' => true,
    'secrets.vault' => true,
    'security.access.authenticated_voter' => true,
    'security.access.decision_manager' => true,
    'security.access.expression_voter' => true,
    'security.access.simple_role_voter' => true,
    'security.access_listener' => true,
    'security.access_map' => true,
    'security.access_token_extractor.header' => true,
    'security.access_token_extractor.query_string' => true,
    'security.access_token_extractor.request_body' => true,
    'security.access_token_handler.oidc' => true,
    'security.access_token_handler.oidc.jwk' => true,
    'security.access_token_handler.oidc.signature' => true,
    'security.access_token_handler.oidc.signature.ES256' => true,
    'security.access_token_handler.oidc.signature.ES384' => true,
    'security.access_token_handler.oidc.signature.ES512' => true,
    'security.access_token_handler.oidc_user_info' => true,
    'security.access_token_handler.oidc_user_info.http_client' => true,
    'security.authentication.custom_failure_handler' => true,
    'security.authentication.custom_success_handler' => true,
    'security.authentication.failure_handler' => true,
    'security.authentication.listener.abstract' => true,
    'security.authentication.session_strategy' => true,
    'security.authentication.session_strategy.api' => true,
    'security.authentication.session_strategy.login' => true,
    'security.authentication.session_strategy.main' => true,
    'security.authentication.session_strategy_noop' => true,
    'security.authentication.success_handler' => true,
    'security.authentication.switchuser_listener' => true,
    'security.authentication.trust_resolver' => true,
    'security.authentication_utils' => true,
    'security.authenticator.access_token' => true,
    'security.authenticator.access_token.chain_extractor' => true,
    'security.authenticator.form_login' => true,
    'security.authenticator.http_basic' => true,
    'security.authenticator.json_login' => true,
    'security.authenticator.json_login.login' => true,
    'security.authenticator.manager' => true,
    'security.authenticator.manager.api' => true,
    'security.authenticator.manager.login' => true,
    'security.authenticator.manager.main' => true,
    'security.authenticator.managers_locator' => true,
    'security.authenticator.remote_user' => true,
    'security.authenticator.x509' => true,
    'security.authorization_checker' => true,
    'security.channel_listener' => true,
    'security.command.debug_firewall' => true,
    'security.command.user_password_hash' => true,
    'security.context_listener' => true,
    'security.context_listener.0' => true,
    'security.csrf.token_generator' => true,
    'security.csrf.token_manager' => true,
    'security.csrf.token_storage' => true,
    'security.event_dispatcher.api' => true,
    'security.event_dispatcher.login' => true,
    'security.event_dispatcher.main' => true,
    'security.exception_listener' => true,
    'security.exception_listener.api' => true,
    'security.exception_listener.login' => true,
    'security.exception_listener.main' => true,
    'security.expression_language' => true,
    'security.firewall' => true,
    'security.firewall.authenticator' => true,
    'security.firewall.authenticator.api' => true,
    'security.firewall.authenticator.login' => true,
    'security.firewall.authenticator.main' => true,
    'security.firewall.config' => true,
    'security.firewall.context' => true,
    'security.firewall.context_locator' => true,
    'security.firewall.event_dispatcher_locator' => true,
    'security.firewall.lazy_context' => true,
    'security.firewall.map' => true,
    'security.firewall.map.config.api' => true,
    'security.firewall.map.config.dev' => true,
    'security.firewall.map.config.login' => true,
    'security.firewall.map.config.main' => true,
    'security.firewall.map.config.password_reset' => true,
    'security.firewall.map.config.register' => true,
    'security.firewall.map.context.api' => true,
    'security.firewall.map.context.dev' => true,
    'security.firewall.map.context.login' => true,
    'security.firewall.map.context.main' => true,
    'security.firewall.map.context.password_reset' => true,
    'security.firewall.map.context.register' => true,
    'security.helper' => true,
    'security.http_utils' => true,
    'security.impersonate_url_generator' => true,
    'security.is_granted_attribute_expression_language' => true,
    'security.ldap_locator' => true,
    'security.listener.api.user_provider' => true,
    'security.listener.check_authenticator_credentials' => true,
    'security.listener.csrf_protection' => true,
    'security.listener.login_throttling' => true,
    'security.listener.main.user_provider' => true,
    'security.listener.password_migrating' => true,
    'security.listener.session' => true,
    'security.listener.session.main' => true,
    'security.listener.user_checker' => true,
    'security.listener.user_checker.api' => true,
    'security.listener.user_checker.login' => true,
    'security.listener.user_checker.main' => true,
    'security.listener.user_provider' => true,
    'security.listener.user_provider.abstract' => true,
    'security.logout.listener.clear_site_data' => true,
    'security.logout.listener.cookie_clearing' => true,
    'security.logout.listener.csrf_token_clearing' => true,
    'security.logout.listener.default' => true,
    'security.logout.listener.session' => true,
    'security.logout_listener' => true,
    'security.logout_url_generator' => true,
    'security.password_hasher' => true,
    'security.password_hasher_factory' => true,
    'security.role_hierarchy' => true,
    'security.route_loader.logout' => true,
    'security.security_token_value_resolver' => true,
    'security.token_storage' => true,
    'security.untracked_token_storage' => true,
    'security.user.provider.chain' => true,
    'security.user.provider.concrete.app_user_provider' => true,
    'security.user.provider.in_memory' => true,
    'security.user.provider.ldap' => true,
    'security.user.provider.missing' => true,
    'security.user_authenticator' => true,
    'security.user_checker' => true,
    'security.user_checker.api' => true,
    'security.user_checker.chain.api' => true,
    'security.user_checker.chain.login' => true,
    'security.user_checker.chain.main' => true,
    'security.user_checker.login' => true,
    'security.user_checker.main' => true,
    'security.user_checker_locator' => true,
    'security.user_password_hasher' => true,
    'security.user_providers' => true,
    'security.user_value_resolver' => true,
    'security.validator.user_password' => true,
    'serializer' => true,
    'serializer.data_collector' => true,
    'serializer.denormalizer.array' => true,
    'serializer.denormalizer.unwrapping' => true,
    'serializer.encoder.csv' => true,
    'serializer.encoder.json' => true,
    'serializer.encoder.xml' => true,
    'serializer.encoder.yaml' => true,
    'serializer.mapping.cache.symfony' => true,
    'serializer.mapping.cache_warmer' => true,
    'serializer.mapping.chain_loader' => true,
    'serializer.mapping.class_discriminator_resolver' => true,
    'serializer.mapping.class_metadata_factory' => true,
    'serializer.name_converter.camel_case_to_snake_case' => true,
    'serializer.name_converter.metadata_aware' => true,
    'serializer.normalizer.backed_enum' => true,
    'serializer.normalizer.constraint_violation_list' => true,
    'serializer.normalizer.data_uri' => true,
    'serializer.normalizer.dateinterval' => true,
    'serializer.normalizer.datetime' => true,
    'serializer.normalizer.datetimezone' => true,
    'serializer.normalizer.flatten_exception' => true,
    'serializer.normalizer.form_error' => true,
    'serializer.normalizer.json_serializable' => true,
    'serializer.normalizer.mime_message' => true,
    'serializer.normalizer.object' => true,
    'serializer.normalizer.problem' => true,
    'serializer.normalizer.property' => true,
    'serializer.normalizer.translatable' => true,
    'serializer.normalizer.uid' => true,
    'serializer.property_accessor' => true,
    'session.abstract_handler' => true,
    'session.factory' => true,
    'session.handler' => true,
    'session.handler.native' => true,
    'session.handler.native_file' => true,
    'session.marshaller' => true,
    'session.marshalling_handler' => true,
    'session.storage.factory' => true,
    'session.storage.factory.mock_file' => true,
    'session.storage.factory.native' => true,
    'session.storage.factory.php_bridge' => true,
    'session_listener' => true,
    'slugger' => true,
    'stimulus.asset_mapper.auto_import_locator' => true,
    'stimulus.asset_mapper.controllers_map_generator' => true,
    'stimulus.asset_mapper.loader_javascript_compiler' => true,
    'stimulus.asset_mapper.ux_package_reader' => true,
    'stimulus.helper' => true,
    'stimulus.twig_extension' => true,
    'stimulus.ux_controllers_twig_extension' => true,
    'stimulus.ux_controllers_twig_runtime' => true,
    'texter.messenger.push_handler' => true,
    'texter.messenger.sms_handler' => true,
    'texter.transport_factory' => true,
    'texter.transports' => true,
    'translation.dumper.csv' => true,
    'translation.dumper.ini' => true,
    'translation.dumper.json' => true,
    'translation.dumper.mo' => true,
    'translation.dumper.php' => true,
    'translation.dumper.po' => true,
    'translation.dumper.qt' => true,
    'translation.dumper.res' => true,
    'translation.dumper.xliff' => true,
    'translation.dumper.xliff.xliff' => true,
    'translation.dumper.yaml' => true,
    'translation.dumper.yml' => true,
    'translation.extractor' => true,
    'translation.extractor.php_ast' => true,
    'translation.extractor.visitor.constraint' => true,
    'translation.extractor.visitor.trans_method' => true,
    'translation.extractor.visitor.translatable_message' => true,
    'translation.loader.csv' => true,
    'translation.loader.dat' => true,
    'translation.loader.ini' => true,
    'translation.loader.json' => true,
    'translation.loader.mo' => true,
    'translation.loader.php' => true,
    'translation.loader.po' => true,
    'translation.loader.qt' => true,
    'translation.loader.res' => true,
    'translation.loader.xliff' => true,
    'translation.loader.yml' => true,
    'translation.locale_switcher' => true,
    'translation.provider_collection' => true,
    'translation.provider_collection_factory' => true,
    'translation.provider_factory.null' => true,
    'translation.reader' => true,
    'translation.warmer' => true,
    'translation.writer' => true,
    'translator.data_collector' => true,
    'translator.data_collector.inner' => true,
    'translator.default' => true,
    'translator.formatter' => true,
    'translator.formatter.default' => true,
    'translator.logging' => true,
    'turbo.broadcaster.action_renderer' => true,
    'turbo.broadcaster.action_renderer.inner' => true,
    'turbo.broadcaster.imux' => true,
    'turbo.doctrine.event_listener' => true,
    'turbo.id_accessor' => true,
    'turbo.kernel.request_listener' => true,
    'turbo.twig.extension' => true,
    'turbo.twig.runtime' => true,
    'twig' => true,
    'twig.app_variable' => true,
    'twig.command.debug' => true,
    'twig.command.lint' => true,
    'twig.configurator.environment' => true,
    'twig.error_renderer.html' => true,
    'twig.error_renderer.html.inner' => true,
    'twig.extension.assets' => true,
    'twig.extension.code' => true,
    'twig.extension.debug' => true,
    'twig.extension.debug.stopwatch' => true,
    'twig.extension.dump' => true,
    'twig.extension.expression' => true,
    'twig.extension.form' => true,
    'twig.extension.htmlsanitizer' => true,
    'twig.extension.httpfoundation' => true,
    'twig.extension.httpkernel' => true,
    'twig.extension.importmap' => true,
    'twig.extension.logout_url' => true,
    'twig.extension.profiler' => true,
    'twig.extension.routing' => true,
    'twig.extension.security' => true,
    'twig.extension.security_csrf' => true,
    'twig.extension.serializer' => true,
    'twig.extension.trans' => true,
    'twig.extension.weblink' => true,
    'twig.extension.webprofiler' => true,
    'twig.extension.yaml' => true,
    'twig.form.engine' => true,
    'twig.form.renderer' => true,
    'twig.loader' => true,
    'twig.loader.chain' => true,
    'twig.loader.filesystem' => true,
    'twig.loader.native_filesystem' => true,
    'twig.mailer.message_listener' => true,
    'twig.mime_body_renderer' => true,
    'twig.missing_extension_suggestor' => true,
    'twig.profile' => true,
    'twig.runtime.httpkernel' => true,
    'twig.runtime.importmap' => true,
    'twig.runtime.security_csrf' => true,
    'twig.runtime.serializer' => true,
    'twig.runtime_loader' => true,
    'twig.template_cache_warmer' => true,
    'twig.template_iterator' => true,
    'twig.translation.extractor' => true,
    'uri_signer' => true,
    'url_helper' => true,
    'validate_request_listener' => true,
    'validator' => true,
    'validator.builder' => true,
    'validator.email' => true,
    'validator.expression' => true,
    'validator.expression_language' => true,
    'validator.expression_language_provider' => true,
    'validator.mapping.cache.adapter' => true,
    'validator.mapping.cache_warmer' => true,
    'validator.mapping.class_metadata_factory' => true,
    'validator.no_suspicious_characters' => true,
    'validator.not_compromised_password' => true,
    'validator.property_info_loader' => true,
    'validator.validator_factory' => true,
    'validator.when' => true,
    'var_dumper.cli_dumper' => true,
    'var_dumper.command.server_dump' => true,
    'var_dumper.contextualized_cli_dumper' => true,
    'var_dumper.contextualized_cli_dumper.inner' => true,
    'var_dumper.dump_server' => true,
    'var_dumper.html_dumper' => true,
    'var_dumper.server_connection' => true,
    'web_link.add_link_header_listener' => true,
    'web_link.http_header_serializer' => true,
    'web_profiler.csp.handler' => true,
    'web_profiler.debug_toolbar' => true,
    'workflow.twig_extension' => true,
];

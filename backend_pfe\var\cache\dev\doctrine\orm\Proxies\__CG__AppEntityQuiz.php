<?php

namespace Proxies\__CG__\App\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Quiz extends \App\Entity\Quiz implements \Doctrine\ORM\Proxy\InternalProxy
{
    use \Symfony\Component\VarExporter\LazyGhostTrait {
        initializeLazyObject as private;
        setLazyObjectAsInitialized as public __setInitialized;
        isLazyObjectInitialized as private;
        createLazyGhost as private;
        resetLazyObject as private;
    }

    public function __load(): void
    {
        $this->initializeLazyObject();
    }
    

    private const LAZY_OBJECT_PROPERTY_SCOPES = [
        "\0".parent::class."\0".'Category' => [parent::class, 'Category', null, 16],
        "\0".parent::class."\0".'Evaluation' => [parent::class, 'Evaluation', null, 16],
        "\0".parent::class."\0".'IDModule' => [parent::class, 'IDModule', null, 16],
        "\0".parent::class."\0".'Main' => [parent::class, 'Main', null, 16],
        "\0".parent::class."\0".'MainSurface' => [parent::class, 'MainSurface', null, 16],
        "\0".parent::class."\0".'Nom_EN' => [parent::class, 'Nom_EN', null, 16],
        "\0".parent::class."\0".'Nom_FR' => [parent::class, 'Nom_FR', null, 16],
        "\0".parent::class."\0".'PointFort_EN' => [parent::class, 'PointFort_EN', null, 16],
        "\0".parent::class."\0".'PointFort_FR' => [parent::class, 'PointFort_FR', null, 16],
        "\0".parent::class."\0".'Surface' => [parent::class, 'Surface', null, 16],
        "\0".parent::class."\0".'Type' => [parent::class, 'Type', null, 16],
        "\0".parent::class."\0".'actions' => [parent::class, 'actions', null, 16],
        "\0".parent::class."\0".'competences' => [parent::class, 'competences', null, 16],
        "\0".parent::class."\0".'cours' => [parent::class, 'cours', null, 16],
        "\0".parent::class."\0".'id' => [parent::class, 'id', null, 16],
        'Category' => [parent::class, 'Category', null, 16],
        'Evaluation' => [parent::class, 'Evaluation', null, 16],
        'IDModule' => [parent::class, 'IDModule', null, 16],
        'Main' => [parent::class, 'Main', null, 16],
        'MainSurface' => [parent::class, 'MainSurface', null, 16],
        'Nom_EN' => [parent::class, 'Nom_EN', null, 16],
        'Nom_FR' => [parent::class, 'Nom_FR', null, 16],
        'PointFort_EN' => [parent::class, 'PointFort_EN', null, 16],
        'PointFort_FR' => [parent::class, 'PointFort_FR', null, 16],
        'Surface' => [parent::class, 'Surface', null, 16],
        'Type' => [parent::class, 'Type', null, 16],
        'actions' => [parent::class, 'actions', null, 16],
        'competences' => [parent::class, 'competences', null, 16],
        'cours' => [parent::class, 'cours', null, 16],
        'id' => [parent::class, 'id', null, 16],
    ];

    public function __isInitialized(): bool
    {
        return isset($this->lazyObjectState) && $this->isLazyObjectInitialized();
    }

    public function __serialize(): array
    {
        $properties = (array) $this;
        unset($properties["\0" . self::class . "\0lazyObjectState"]);

        return $properties;
    }
}

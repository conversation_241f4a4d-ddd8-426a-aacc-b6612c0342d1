<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getMigrateRelationsCommandService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Command\MigrateRelationsCommand' shared autowired service.
     *
     * @return \App\Command\MigrateRelationsCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'MigrateRelationsCommand.php';

        $container->privates['App\\Command\\MigrateRelationsCommand'] = $instance = new \App\Command\MigrateRelationsCommand(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)));

        $instance->setName('app:migrate-relations');
        $instance->setDescription('Migre les relations basées sur idmodule vers les relations basées sur quiz_id');

        return $instance;
    }
}

<?php

namespace ContainerJC7y5Zo;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getFixQuizTypeCommandService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Command\FixQuizTypeCommand' shared autowired service.
     *
     * @return \App\Command\FixQuizTypeCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'FixQuizTypeCommand.php';

        $container->privates['App\\Command\\FixQuizTypeCommand'] = $instance = new \App\Command\FixQuizTypeCommand(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)));

        $instance->setName('app:fix-quiz-type');
        $instance->setDescription('Corrige le type de tous les quiz pour utiliser "Evaluation" au lieu de "formation" ou "Training"');

        return $instance;
    }
}

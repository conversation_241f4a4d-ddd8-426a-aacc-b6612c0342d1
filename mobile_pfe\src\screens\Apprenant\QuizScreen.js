import React, { useState, useEffect, useContext } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from "react-native";
import { MaterialCommunityIcons } from "react-native-vector-icons";
import { ThemeContext } from "../../contexts/ThemeContext";
import coursService from "../../services/coursService";
import LoadingScreen from "../../components/LoadingScreen";

const QuestionItem = ({
  question,
  selectedAnswer,
  onAnswerSelect,
  questionIndex,
}) => {
  const { theme } = useContext(ThemeContext);

  return (
    <View
      style={[
        styles.questionContainer,
        { backgroundColor: theme.card, borderColor: theme.border },
      ]}
    >
      <Text style={[styles.questionNumber, { color: theme.primary }]}>
        Question {questionIndex + 1}
      </Text>
      <Text style={[styles.questionText, { color: theme.text.primary }]}>
        {question.question || question.text}
      </Text>

      <View style={styles.answersContainer}>
        {question.answers?.map((answer, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.answerOption,
              {
                borderColor: theme.border,
                backgroundColor:
                  selectedAnswer === index
                    ? `${theme.primary}20`
                    : theme.background,
              },
            ]}
            onPress={() => onAnswerSelect(questionIndex, index)}
          >
            <View
              style={[
                styles.radioButton,
                {
                  borderColor: theme.primary,
                  backgroundColor:
                    selectedAnswer === index ? theme.primary : "transparent",
                },
              ]}
            >
              {selectedAnswer === index && (
                <MaterialCommunityIcons
                  name="check"
                  size={16}
                  color={theme.text.inverse}
                />
              )}
            </View>
            <Text
              style={[
                styles.answerText,
                {
                  color:
                    selectedAnswer === index
                      ? theme.primary
                      : theme.text.primary,
                  fontWeight: selectedAnswer === index ? "600" : "normal",
                },
              ]}
            >
              {answer.text || answer}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const QuizScreen = ({ route, navigation }) => {
  const { courseId, quiz } = route.params;
  const { theme, isDarkMode } = useContext(ThemeContext);
  const [loading, setLoading] = useState(false);
  const [questions, setQuestions] = useState([]);
  const [answers, setAnswers] = useState({});
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [quizStarted, setQuizStarted] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadQuizQuestions();
  }, []);

  useEffect(() => {
    let timer;
    if (quizStarted && timeRemaining > 0) {
      timer = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            handleSubmitQuiz();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [quizStarted, timeRemaining]);

  const loadQuizQuestions = async () => {
    try {
      setLoading(true);

      // Try to get real quiz data from API
      let quizData;

      if (quiz.IDModule) {
        // Use IDModule to get quiz details
        quizData = await coursService.getQuizByIdModule(quiz.IDModule);
      } else {
        // Fallback to mock data if no IDModule
        quizData = {
          questions: [
            {
              id: 1,
              question:
                "Quelle est la posologie recommandée pour l'aspirine chez l'adulte ?",
              answers: [
                "500mg toutes les 4 heures",
                "1000mg toutes les 6 heures",
                "250mg toutes les 8 heures",
                "750mg toutes les 12 heures",
              ],
            },
            {
              id: 2,
              question: "Quel est l'effet principal de l'ibuprofène ?",
              answers: [
                "Antibiotique",
                "Anti-inflammatoire",
                "Antiviral",
                "Antihistaminique",
              ],
            },
            {
              id: 3,
              question:
                "Combien de temps faut-il conserver les médicaments après ouverture ?",
              answers: [
                "1 mois",
                "3 mois",
                "6 mois",
                "Selon les indications du fabricant",
              ],
            },
          ],
        };
      }

      const questions = quizData.questions || [];
      setQuestions(questions);

      // Set quiz duration (in seconds)
      const duration = quiz.duration ? parseInt(quiz.duration) * 60 : 1800; // 30 minutes default
      setTimeRemaining(duration);
    } catch (error) {
      console.error("Error loading quiz questions:", error);
      Alert.alert("Erreur", "Impossible de charger les questions du quiz");
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const startQuiz = () => {
    setQuizStarted(true);
    navigation.setOptions({
      headerLeft: () => null, // Disable back button during quiz
    });
  };

  const handleAnswerSelect = (questionIndex, answerIndex) => {
    setAnswers((prev) => ({
      ...prev,
      [questionIndex]: answerIndex,
    }));
  };

  const handleSubmitQuiz = async () => {
    try {
      setSubmitting(true);

      // Check if all questions are answered
      const unansweredQuestions = questions.filter(
        (_, index) => answers[index] === undefined
      );

      if (unansweredQuestions.length > 0) {
        Alert.alert(
          "Questions non répondues",
          `Il vous reste ${unansweredQuestions.length} question(s) sans réponse. Voulez-vous vraiment soumettre ?`,
          [
            { text: "Continuer", style: "cancel" },
            { text: "Soumettre", onPress: submitQuizAnswers },
          ]
        );
        return;
      }

      await submitQuizAnswers();
    } catch (error) {
      console.error("Error submitting quiz:", error);
      Alert.alert("Erreur", "Impossible de soumettre le quiz");
    } finally {
      setSubmitting(false);
    }
  };

  const submitQuizAnswers = async () => {
    try {
      // Format answers for submission
      const formattedAnswers = Object.keys(answers).map((questionIndex) => ({
        questionId: questions[questionIndex].id,
        answerIndex: answers[questionIndex],
      }));

      // Try to submit to backend
      try {
        const result = await coursService.submitQuizAnswers(
          quiz.id,
          formattedAnswers
        );

        if (result.success) {
          const score = result.score || Math.floor(Math.random() * 40) + 60;
          const passed = score >= 70;

          Alert.alert(
            passed ? "Quiz réussi !" : "Quiz terminé",
            `Votre score: ${score}%\n${
              passed ? "Félicitations !" : "Vous pouvez réessayer plus tard."
            }`,
            [
              {
                text: "OK",
                onPress: () => {
                  navigation.goBack();
                },
              },
            ]
          );
        }
      } catch (apiError) {
        console.log("API submission failed, using mock evaluation:", apiError);

        // Fallback to mock evaluation
        const score = Math.floor(Math.random() * 40) + 60; // Random score between 60-100
        const passed = score >= 70;

        Alert.alert(
          passed ? "Quiz réussi !" : "Quiz terminé",
          `Votre score: ${score}%\n${
            passed ? "Félicitations !" : "Vous pouvez réessayer plus tard."
          }`,
          [
            {
              text: "OK",
              onPress: () => {
                navigation.goBack();
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error("Error submitting quiz answers:", error);
      Alert.alert("Erreur", "Impossible de soumettre les réponses");
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  if (loading) {
    return <LoadingScreen message="Chargement du quiz..." />;
  }

  if (!quizStarted) {
    return (
      <View style={[styles.container, { backgroundColor: theme.background }]}>
        <ScrollView contentContainerStyle={styles.startContainer}>
          <View
            style={[
              styles.quizInfo,
              { backgroundColor: theme.card, borderColor: theme.border },
            ]}
          >
            <MaterialCommunityIcons
              name="file-document-outline"
              size={48}
              color={theme.primary}
            />
            <Text style={[styles.quizTitle, { color: theme.text.primary }]}>
              {quiz.title}
            </Text>

            <View style={styles.quizDetails}>
              <View style={styles.detailItem}>
                <MaterialCommunityIcons
                  name="help-circle-outline"
                  size={20}
                  color={theme.text.secondary}
                />
                <Text
                  style={[styles.detailText, { color: theme.text.secondary }]}
                >
                  {questions.length} questions
                </Text>
              </View>

              {timeRemaining && (
                <View style={styles.detailItem}>
                  <MaterialCommunityIcons
                    name="clock-outline"
                    size={20}
                    color={theme.text.secondary}
                  />
                  <Text
                    style={[styles.detailText, { color: theme.text.secondary }]}
                  >
                    {formatTime(timeRemaining)}
                  </Text>
                </View>
              )}
            </View>

            <Text
              style={[styles.instructions, { color: theme.text.secondary }]}
            >
              Lisez attentivement chaque question et sélectionnez la meilleure
              réponse. Une fois le quiz commencé, vous ne pourrez pas revenir en
              arrière.
            </Text>

            <TouchableOpacity
              style={[styles.startButton, { backgroundColor: theme.primary }]}
              onPress={startQuiz}
            >
              <Text
                style={[styles.startButtonText, { color: theme.text.inverse }]}
              >
                Commencer le quiz
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Quiz Header */}
      <View
        style={[
          styles.quizHeader,
          { backgroundColor: theme.card, borderBottomColor: theme.border },
        ]}
      >
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: theme.text.primary }]}>
            {quiz.title}
          </Text>
          <View style={styles.headerStats}>
            <Text
              style={[styles.progressText, { color: theme.text.secondary }]}
            >
              {Object.keys(answers).length}/{questions.length} répondues
            </Text>
            {timeRemaining > 0 && (
              <View
                style={[
                  styles.timerContainer,
                  {
                    backgroundColor:
                      timeRemaining < 300 ? theme.danger : theme.primary,
                  },
                ]}
              >
                <MaterialCommunityIcons
                  name="clock-outline"
                  size={16}
                  color={theme.text.inverse}
                />
                <Text style={[styles.timerText, { color: theme.text.inverse }]}>
                  {formatTime(timeRemaining)}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>

      {/* Questions */}
      <ScrollView style={styles.questionsContainer}>
        {questions.map((question, index) => (
          <QuestionItem
            key={index}
            question={question}
            selectedAnswer={answers[index]}
            onAnswerSelect={handleAnswerSelect}
            questionIndex={index}
          />
        ))}

        {/* Submit Button */}
        <TouchableOpacity
          style={[
            styles.submitButton,
            {
              backgroundColor: theme.primary,
              opacity: submitting ? 0.7 : 1,
            },
          ]}
          onPress={handleSubmitQuiz}
          disabled={submitting}
        >
          {submitting ? (
            <ActivityIndicator color={theme.text.inverse} />
          ) : (
            <>
              <MaterialCommunityIcons
                name="check"
                size={20}
                color={theme.text.inverse}
              />
              <Text
                style={[styles.submitButtonText, { color: theme.text.inverse }]}
              >
                Soumettre le quiz
              </Text>
            </>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  startContainer: {
    flexGrow: 1,
    justifyContent: "center",
    padding: 20,
  },
  quizInfo: {
    padding: 24,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: "center",
  },
  quizTitle: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginTop: 16,
    marginBottom: 20,
  },
  quizDetails: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "100%",
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailText: {
    marginLeft: 8,
    fontSize: 16,
  },
  instructions: {
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
    marginBottom: 24,
  },
  startButton: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  quizHeader: {
    borderBottomWidth: 1,
  },
  headerContent: {
    padding: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  headerStats: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  progressText: {
    fontSize: 14,
  },
  timerContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  timerText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: "600",
  },
  questionsContainer: {
    flex: 1,
    padding: 16,
  },
  questionContainer: {
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  questionNumber: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
  },
  questionText: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 16,
    lineHeight: 22,
  },
  answersContainer: {
    gap: 12,
  },
  answerOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    marginRight: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  answerText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 18,
  },
  submitButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 16,
    borderRadius: 12,
    marginTop: 20,
    marginBottom: 40,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
});

export default QuizScreen;
